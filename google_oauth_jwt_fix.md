# Fixing Google OAuth JWT Verification in WallpaperHub

This document explains how to fix the JWT verification issues when using Google OAuth in WallpaperHub.

## The Issue

When trying to sign in with Google, you may encounter one of these errors:

1. `AttributeError: module 'jwt' has no attribute 'PyJWTError'`
2. `TypeError: patched_verify_and_decode() missing 2 required positional arguments: 'token' and 'key'`

These errors occur due to compatibility issues between the PyJWT library and django-allauth.

## Solution

We've implemented a comprehensive solution that fixes these issues:

### 1. Install the Correct Version of PyJWT

```bash
pip install PyJWT==1.7.1
pip install cryptography
```

### 2. Apply the Direct Patch

We've created a management command that directly patches the django-allauth library:

```bash
python manage.py fix_google_oauth
```

This command:
- Patches the `jwtkit.py` file in django-allauth to handle different versions of PyJWT
- Patches the Google OAuth provider's JWT verification function

### 3. Automatic Patching at Runtime

We've also added code to automatically apply these patches when the app starts:

- Created `accounts/direct_jwt_patch.py` to patch the django-allauth JWT module
- Created `accounts/google_jwt_patch.py` to patch the Google OAuth provider
- Updated `accounts/apps.py` to apply these patches when the app starts

## How to Test

1. Make sure you've installed the correct version of PyJWT:
   ```bash
   pip install PyJWT==1.7.1
   pip install cryptography
   ```

2. Run the patch command:
   ```bash
   python manage.py fix_google_oauth
   ```

3. Start the development server:
   ```bash
   python manage.py runserver
   ```

4. Go to the login page and click "Sign in with Google"
   - You should now be able to sign in without any JWT errors

## Technical Details

### The Root Cause

The issue occurs because:

1. Django-allauth expects PyJWT to have a `PyJWTError` attribute, which was removed in newer versions
2. The JWT verification function in django-allauth is not compatible with newer versions of PyJWT
3. The Google OAuth provider's JWT verification function has similar compatibility issues

### Our Solution

Our solution addresses these issues by:

1. Using a compatible version of PyJWT (1.7.1)
2. Patching the django-allauth JWT module to handle different versions of PyJWT
3. Patching the Google OAuth provider's JWT verification function
4. Applying these patches both at runtime and through a management command

This ensures maximum compatibility and reliability, even if dependencies are updated in the future.
