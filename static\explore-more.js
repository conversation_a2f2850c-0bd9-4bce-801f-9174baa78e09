// This file is now deprecated. The functionality has been moved directly into userHome.html
// to avoid conflicts between multiple event handlers.
console.log('explore-more.js: This file is deprecated. Using inline implementation instead.');

// We're not attaching any event listeners from this file anymore to avoid conflicts
document.addEventListener('DOMContentLoaded', function() {
    console.log('explore-more.js: External script loaded but not attaching event listeners');

    // The loadMoreImages function is now defined directly in the HTML file
    // and attached to the button via the onclick attribute
});
