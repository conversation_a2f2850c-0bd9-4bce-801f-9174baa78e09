/* Smooth Scrolling Styles for WallpaperHub */

/* Apply smooth scrolling to the entire document */
html {
    scroll-behavior: smooth;
}

/* Scroll to top button */
#scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--primary, #65558F);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 9999;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    border: 2px solid white;
    padding: 0;
    outline: none;
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
    touch-action: manipulation; /* Improve touch behavior */
}

#scroll-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

#scroll-to-top:hover {
    background-color: var(--primary-dark, #534679);
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

#scroll-to-top:active {
    transform: scale(0.95);
}

#scroll-to-top i {
    font-size: 24px;
}

/* Mobile-specific styles */
#scroll-to-top.mobile {
    width: 55px;
    height: 55px;
    border: 3px solid white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
}

#scroll-to-top.mobile i {
    font-size: 26px;
}

/* Smooth transition for all elements */
a, button, .nav-link, .btn {
    transition: all 0.3s ease;
}

/* Smooth hover effects for cards */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Smooth animation for page transitions */
.page-transition {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Smooth scrolling for sections */
section {
    scroll-margin-top: 80px; /* Adjust based on navbar height */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #scroll-to-top {
        bottom: 20px;
        right: 20px;
        width: 60px;  /* Larger touch target for mobile */
        height: 60px; /* Larger touch target for mobile */
        z-index: 9999; /* Ensure it's above all other elements */
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5); /* More visible shadow */
        border: 3px solid white; /* More visible border */
    }

    #scroll-to-top.show {
        opacity: 1; /* Fully visible on mobile */
    }

    #scroll-to-top i {
        font-size: 28px; /* Larger icon for better visibility */
    }

    /* Fix for iOS Safari */
    #scroll-to-top {
        -webkit-appearance: none;
        -webkit-touch-callout: none;
    }

    /* Fix for Android Chrome */
    #scroll-to-top:focus {
        outline: none;
    }
}

@media (max-width: 768px) {
    section {
        scroll-margin-top: 60px; /* Adjust for mobile navbar height */
    }
}
