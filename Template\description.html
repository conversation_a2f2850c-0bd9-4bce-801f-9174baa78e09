{% load static %}
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ wallpaper.title|default:"Beautiful Wallpaper" }} - WallpaperHub</title>

    <!-- Favicon -->
    <link rel="icon" href="{% static 'favicon.svg' %}" type="image/svg+xml">
    <link rel="alternate icon" href="{% static 'favicon.ico' %}" type="image/x-icon">
    <link rel="shortcut icon" href="{% static 'favicon.ico' %}" type="image/x-icon">
    <link rel="apple-touch-icon" href="{% static 'apple-touch-icon.png' %}">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">

    <!-- Theme Styles -->
    <link rel="stylesheet" href="{% static 'css/theme-styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/smooth-scroll.css' %}">
    <script src="{% static 'js/theme-init.js' %}"></script>

    <style>
        :root {
            --primary: #65558F;
            --primary-dark: #534979;
            --secondary: #F8F6FF;
            --dark: #333333;
            --light: #FFFFFF;
            --gray: #F5F5F5;
            --success: #4CAF50;
            --danger: #F44336;
            --warning: #FF9800;
            --info: #2196F3;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        /* Header */
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 30px;
            background-color: var(--navbar-bg);
            box-shadow: 0 2px 10px var(--card-shadow);
            position: sticky;
            top: 0;
            z-index: 100;
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 30px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .logout-btn:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
        }

        /* Main Content */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Back Button */
        .back-btn {
            display: inline-flex;
            align-items: center;
            margin: 20px 0;
            color: var(--dark);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            color: var(--primary);
            transform: translateX(-5px);
        }

        .back-btn i {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        /* Wallpaper Content */
        .wallpaper-content {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-top: 20px;
            background-color: var(--light);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .wallpaper-image-container {
            flex: 1;
            min-width: 300px;
            position: relative;
            background-color: #000;
        }

        .wallpaper-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            display: block;
            max-height: 70vh;
            position: relative;
        }

        .no-image-placeholder {
            width: 100%;
            height: 100%;
            min-height: 300px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            color: #999;
        }

        .no-image-placeholder i {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .wallpaper-details {
            flex: 1;
            min-width: 300px;
            padding: 30px;
            display: flex;
            flex-direction: column;
        }

        .wallpaper-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 15px;
        }

        .wallpaper-description {
            margin-bottom: 30px;
            color: #555;
            flex-grow: 1;
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .action-btn .icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 1.5rem;
            transition: all 0.3s ease;
            background-color: var(--gray);
        }

        .action-btn .label {
            font-size: 0.9rem;
            font-weight: 500;
        }

        .action-btn .count {
            font-size: 0.8rem;
            color: #777;
            margin-top: 5px;
        }

        /* Download Button */
        .download-btn .icon {
            color: var(--primary);
        }

        .download-btn:hover .icon {
            background-color: var(--primary);
            color: white;
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(101, 85, 143, 0.3);
        }

        /* Like Button */
        .like-btn .icon {
            color: #F44336;
        }

        .like-btn:hover .icon {
            background-color: #ffebee;
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(244, 67, 54, 0.3);
        }

        .like-btn.active .icon {
            background-color: #F44336;
            color: white;
        }

        /* Save Button */
        .save-btn .icon {
            color: #2196F3;
        }

        .save-btn:hover .icon {
            background-color: #e3f2fd;
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
        }

        .save-btn.active .icon {
            background-color: #2196F3;
            color: white;
        }

        /* Share Button */
        .share-btn .icon {
            color: #2196F3;
        }

        .share-btn:hover .icon {
            background-color: #e3f2fd;
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
        }

        /* Share Modal */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .share-modal {
            background-color: white;
            border-radius: 15px;
            padding: 25px;
            width: 90%;
            max-width: 400px;
            transform: translateY(20px);
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .modal-overlay.active .share-modal {
            transform: translateY(0);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark);
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #777;
            transition: all 0.3s ease;
        }

        .close-modal:hover {
            color: var(--danger);
            transform: rotate(90deg);
        }

        .share-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .share-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .share-option:hover {
            background-color: #f5f5f5;
            transform: translateY(-3px);
        }

        .share-option i {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .share-option span {
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Fallback download button */
        .fallback-download-btn {
            display: none;
            margin-top: 15px;
            padding: 8px 15px;
            background-color: var(--info);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
        }

        .fallback-download-btn:hover {
            background-color: #0b7dda;
            transform: translateY(-2px);
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(33, 150, 243, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
            }
        }

        .whatsapp { color: #25D366; }
        .facebook { color: #1877F2; }
        .instagram { color: #E4405F; }
        .snapchat { color: #FFFC00; }

        /* Toast Notification */
        .toast-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }



        .toast {
            background-color: white;
            border-radius: 10px;
            padding: 15px 20px;
            margin-bottom: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            animation: slideIn 0.3s ease forwards;
            max-width: 300px;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .toast.success { border-left: 4px solid var(--success); }
        .toast.error { border-left: 4px solid var(--danger); }

        .toast i {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .toast.success i { color: var(--success); }
        .toast.error i { color: var(--danger); }

        /* Related Wallpapers */
        .related-section {
            margin-top: 40px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--secondary);
        }

        .related-wallpapers {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }

        .related-item {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
        }

        .related-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }

        .related-item img {
            width: 100%;
            height: 180px;
            object-fit: cover;
            display: block;
            transition: transform 0.3s ease;
        }

        .related-item:hover img {
            transform: scale(1.05);
        }

        .related-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
            padding: 15px;
            color: white;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .related-item:hover .related-overlay {
            opacity: 1;
        }

        .related-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        /* Footer */
        footer {
            background-color: var(--dark);
            color: var(--light);
            text-align: center;
            padding: 20px;
            margin-top: 50px;
        }

        footer a {
            color: var(--secondary);
            text-decoration: none;
            margin: 0 10px;
            transition: color 0.3s ease;
        }

        footer a:hover {
            color: white;
            text-decoration: underline;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .wallpaper-content {
                flex-direction: column;
            }

            .wallpaper-image {
                max-height: 50vh;
            }

            .action-buttons {
                justify-content: space-around;
            }

            .related-wallpapers {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <a href="{{ back_url }}" class="logo">WallpaperHub</a>
        <div class="header-actions">
            <a href="/logout/" class="logout-btn">Logout</a>
        </div>
    </header>

    <div class="container">
        <!-- Back Button -->
        <a href="{{ back_url }}" class="back-btn">
            <i class="fas fa-arrow-left"></i>
            {% if request.GET.from_detail %}
                Back to Previous Wallpaper
            {% else %}
                Back to Gallery{% if search_query %} ({{ search_query }}){% endif %}
            {% endif %}
        </a>

        <!-- Wallpaper Content -->
        <div class="wallpaper-content">
            <div class="wallpaper-image-container">
                {% if wallpaper.urls.full %}
                <img src="{{ wallpaper.urls.full }}" alt="{{ wallpaper.alt_description }}" class="wallpaper-image">
                {% else %}
                <div class="no-image-placeholder">
                    <i class="fas fa-image"></i>
                    <p>Image not available</p>
                </div>
                {% endif %}
            </div>
            <div class="wallpaper-details">
                <h1 class="wallpaper-title">{{ wallpaper.title|default:wallpaper.alt_description|default:"Beautiful Wallpaper" }}</h1>
                <p class="wallpaper-description">
                    {% if wallpaper.description %}
                        {{ wallpaper.description }}
                    {% else %}
                        This stunning wallpaper is perfect for your desktop or mobile device. Download it now to enhance your screen with this beautiful imagery.
                    {% endif %}
                </p>
                <div class="action-buttons">
                    {% if wallpaper.id %}
                    <button class="action-btn download-btn" id="download-btn" data-id="{{ wallpaper.id }}">
                        <div class="icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <span class="label">Download</span>
                        <span class="count">{{ wallpaper.downloads|default:"0" }}</span>
                    </button>

                    <button class="action-btn like-btn {% if is_liked %}active{% endif %}" id="like-btn" data-id="{{ wallpaper.id }}">
                        <div class="icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <span class="label">{% if is_liked %}Liked{% else %}Like{% endif %}</span>
                        <span class="count">{{ wallpaper.likes|default:"0" }}</span>
                    </button>

                    <button class="action-btn share-btn" id="share-btn" data-id="{{ wallpaper.id }}">
                        <div class="icon">
                            <i class="fas fa-share-alt"></i>
                        </div>
                        <span class="label">Share</span>
                        <span class="count">{{ wallpaper.shares|default:"0" }}</span>
                    </button>

                    <button class="action-btn save-btn {% if is_saved %}active{% endif %}" id="save-btn" data-id="{{ wallpaper.id }}">
                        <div class="icon">
                            <i class="fas fa-bookmark"></i>
                        </div>
                        <span class="label">{% if is_saved %}Saved{% else %}Save{% endif %}</span>
                    </button>
                    {% else %}
                    <p class="text-muted">Interactive features are not available for this wallpaper.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Related Wallpapers -->
        <div class="related-section">
            <h2 class="section-title">You May Also Like</h2>
            <div class="related-wallpapers">
                {% for related in related_wallpapers %}
                {% if related.id %}{% if related.urls.small or related.custom_upload %}
                <a href="{% url 'wallpaper_detail' id=related.id %}?from_detail={{ wallpaper.id }}" class="related-item">
                    {% if related.custom_upload %}
                        <img src="{{ related.image_path }}" alt="{{ related.title }}">
                        <div class="related-overlay">
                            <h3 class="related-title">{{ related.title }}</h3>
                        </div>
                    {% else %}
                        <img src="{{ related.urls.small }}" alt="{{ related.alt_description }}">
                        <div class="related-overlay">
                            <h3 class="related-title">{{ related.alt_description|default:"Beautiful Wallpaper" }}</h3>
                        </div>
                    {% endif %}
                </a>
                {% endif %}{% endif %}
                {% empty %}
                <p>No related wallpapers found.</p>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Share Modal -->
    <div class="modal-overlay" id="share-modal">
        <div class="share-modal">
            <div class="modal-header">
                <h3 class="modal-title">Share this wallpaper</h3>
                <button class="close-modal" id="close-modal">&times;</button>
            </div>
            <div class="share-options">
                <div class="share-option whatsapp" data-platform="whatsapp">
                    <i class="fab fa-whatsapp"></i>
                    <span>WhatsApp</span>
                </div>
                <div class="share-option facebook" data-platform="facebook">
                    <i class="fab fa-facebook"></i>
                    <span>Facebook</span>
                </div>
                <div class="share-option instagram" data-platform="instagram">
                    <i class="fab fa-instagram"></i>
                    <span>Instagram</span>
                </div>
                <div class="share-option snapchat" data-platform="snapchat">
                    <i class="fab fa-snapchat"></i>
                    <span>Snapchat</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container" id="toast-container"></div>

    <!-- Footer -->
    <footer>
        <a href="/termsOfService.html">Terms of Service</a>
        <a href="/privacyPolicy.html">Privacy Policy</a>
        <a href="/aboutUs.html">About Us</a>
        <p>&copy; 2025 WallpaperHub. All rights reserved.</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Elements
            const downloadBtn = document.getElementById('download-btn');
            const likeBtn = document.getElementById('like-btn');
            const shareBtn = document.getElementById('share-btn');
            const shareModal = document.getElementById('share-modal');
            const closeModal = document.getElementById('close-modal');
            const shareOptions = document.querySelectorAll('.share-option');
            const toastContainer = document.getElementById('toast-container');

            // Download functionality
            if (downloadBtn && downloadBtn.getAttribute('data-id')) {
                downloadBtn.addEventListener('click', function() {
                    const wallpaperId = this.getAttribute('data-id');
                    const downloadCount = this.querySelector('.count');

                    // Send AJAX request to increment download count
                    fetch(`/api/wallpapers/${wallpaperId}/download/`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCookie('csrftoken')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update download counter
                            downloadCount.textContent = data.downloads;

                            // Instead of directly using the URL, we'll use our own endpoint to force download
                            const downloadUrl = `/api/wallpapers/${wallpaperId}/download-file/?url=${encodeURIComponent(data.download_url || '{{ wallpaper.urls.full }}')}`;

                            // Create a proper filename from the wallpaper title or ID
                            const wallpaperTitle = '{{ wallpaper.title|default:"wallpaper"|slugify }}';
                            const filename = wallpaperTitle ? `${wallpaperTitle}-${wallpaperId}.jpg` : `wallpaper-${wallpaperId}.jpg`;

                            // Use a single download method with fallback mechanism

                            // Method 1: Using download attribute (modern browsers)
                            const downloadLink = document.createElement('a');
                            downloadLink.href = downloadUrl;
                            downloadLink.download = filename;
                            downloadLink.style.display = 'none';
                            document.body.appendChild(downloadLink);

                            // Track if download was successful
                            let downloadStarted = false;

                            // Try to start the download
                            try {
                                downloadLink.click();
                                downloadStarted = true;
                            } catch (e) {
                                console.error('Download method 1 failed:', e);
                                downloadStarted = false;
                            }

                            // Show a fallback button in case automatic download fails
                            const fallbackBtn = document.createElement('button');
                            fallbackBtn.textContent = 'Download not starting? Click here';
                            fallbackBtn.className = 'fallback-download-btn';
                            fallbackBtn.style.display = 'none';
                            fallbackBtn.onclick = function() {
                                window.open(downloadUrl, '_blank');
                                this.style.display = 'none';
                            };

                            // Add the fallback button near the download button
                            const actionButtons = document.querySelector('.action-buttons');
                            if (actionButtons) {
                                actionButtons.appendChild(fallbackBtn);

                                // Show the fallback button after a delay if needed
                                setTimeout(() => {
                                    fallbackBtn.style.display = 'block';
                                }, 5000);

                                // Hide it after 20 seconds
                                setTimeout(() => {
                                    fallbackBtn.style.display = 'none';
                                }, 20000);
                            }

                            // Clean up the download link element after a delay
                            setTimeout(() => {
                                if (downloadLink.parentNode) document.body.removeChild(downloadLink);
                            }, 2000);

                            // If primary download method failed, show fallback button immediately
                            if (!downloadStarted) {
                                fallbackBtn.style.display = 'block';
                            }

                            // Show toast notification
                            showToast('Download started', 'success');
                        } else {
                            showToast('Download failed', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast('Failed to download wallpaper', 'error');
                    });
                });
            }

            // Like functionality
            if (likeBtn && likeBtn.getAttribute('data-id')) {
                likeBtn.addEventListener('click', function() {
                    const wallpaperId = this.getAttribute('data-id');
                    const isActive = this.classList.contains('active');
                    const likeCount = this.querySelector('.count');
                    const likeLabel = this.querySelector('.label');

                    // Send AJAX request to like/unlike
                    fetch(`/api/wallpapers/${wallpaperId}/like/`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCookie('csrftoken')
                        },
                        body: JSON.stringify({
                            action: isActive ? 'unlike' : 'like'
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update UI
                            if (isActive) {
                                this.classList.remove('active');
                                likeLabel.textContent = 'Like';
                            } else {
                                this.classList.add('active');
                                likeLabel.textContent = 'Liked';
                            }

                            // Update count
                            likeCount.textContent = data.likes;

                            // Show toast notification
                            showToast(isActive ? 'Removed from favorites' : 'Added to favorites', 'success');
                        } else {
                            showToast('Something went wrong', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast('Failed to update like status', 'error');
                    });
                });
            }

            // Save functionality
            const saveBtn = document.getElementById('save-btn');
            if (saveBtn && saveBtn.getAttribute('data-id')) {
                saveBtn.addEventListener('click', function() {
                    const wallpaperId = this.getAttribute('data-id');
                    const isActive = this.classList.contains('active');
                    const label = this.querySelector('.label');



                    // Update label
                    label.textContent = isActive ? 'Save' : 'Saved';

                    // Send AJAX request to save/unsave wallpaper
                    fetch(`/api/wallpapers/${wallpaperId}/save/`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCookie('csrftoken')
                        },
                        body: JSON.stringify({ action: isActive ? 'unsave' : 'save' })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show toast notification
                            showToast(isActive ? 'Removed from saved wallpapers' : 'Added to saved wallpapers', 'success');
                        } else {
                            // Revert changes if request failed

                            label.textContent = isActive ? 'Saved' : 'Save';
                            showToast('Something went wrong', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        label.textContent = isActive ? 'Saved' : 'Save';
                        showToast('Failed to update save status', 'error');
                    });
                });
            }

            // Share functionality
            if (shareBtn && shareBtn.getAttribute('data-id')) {
                shareBtn.addEventListener('click', function() {
                    const wallpaperId = this.getAttribute('data-id');

                    // Show share modal without incrementing count yet
                    shareModal.classList.add('active');
                });
            }

            // Close modal
            if (closeModal) {
                closeModal.addEventListener('click', function() {
                    shareModal.classList.remove('active');
                });
            }

            // Close modal when clicking outside
            shareModal.addEventListener('click', function(e) {
                if (e.target === shareModal) {
                    shareModal.classList.remove('active');
                }
            });

            // Share options
            shareOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const platform = this.getAttribute('data-platform');
                    const wallpaperUrl = window.location.href;
                    const wallpaperTitle = document.querySelector('.wallpaper-title').textContent;
                    const wallpaperId = shareBtn.getAttribute('data-id');
                    const shareCount = shareBtn.querySelector('.count');

                    let shareUrl = '';
                    let actuallyShared = false;

                    switch(platform) {
                        case 'whatsapp':
                            shareUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(`Check out this amazing wallpaper: ${wallpaperTitle} ${wallpaperUrl}`)}`;
                            actuallyShared = true;
                            break;
                        case 'facebook':
                            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(wallpaperUrl)}`;
                            actuallyShared = true;
                            break;
                        case 'instagram':
                            // Instagram doesn't have a direct share URL, show a toast with instructions
                            showToast('Link copied! Now share it on Instagram', 'success');
                            navigator.clipboard.writeText(wallpaperUrl);
                            actuallyShared = true;
                            shareModal.classList.remove('active');
                            break;
                        case 'snapchat':
                            // Snapchat doesn't have a direct share URL, show a toast with instructions
                            showToast('Link copied! Now share it on Snapchat', 'success');
                            navigator.clipboard.writeText(wallpaperUrl);
                            actuallyShared = true;
                            shareModal.classList.remove('active');
                            break;
                    }

                    // Only increment share count if actually shared
                    if (actuallyShared) {
                        // Send AJAX request to increment share count
                        fetch(`/api/wallpapers/${wallpaperId}/share/`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRFToken': getCookie('csrftoken')
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Update share counter
                                shareCount.textContent = data.shares;
                            }
                        })
                        .catch(error => {
                            console.error('Error updating share count:', error);
                        });
                    }

                    // Open share URL in a new window if available
                    if (shareUrl) {
                        const shareWindow = window.open(shareUrl, '_blank');

                        // Check if popup was blocked
                        if (!shareWindow || shareWindow.closed || typeof shareWindow.closed === 'undefined') {
                            // Popup was blocked, show instructions
                            showToast('Popup blocked. Please copy and share this link manually.', 'warning');
                            navigator.clipboard.writeText(wallpaperUrl);
                        }
                    }

                    // Close modal
                    shareModal.classList.remove('active');
                });
            });

            // Helper function to show toast notifications
            function showToast(message, type) {
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;

                const icon = document.createElement('i');
                icon.className = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';

                const text = document.createElement('span');
                text.textContent = message;

                toast.appendChild(icon);
                toast.appendChild(text);

                toastContainer.appendChild(toast);

                // Remove toast after 3 seconds
                setTimeout(() => {
                    toast.style.opacity = '0';
                    setTimeout(() => {
                        toast.remove();
                    }, 300);
                }, 3000);
            }

            // Helper function to get CSRF token
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }
        });
    </script>

    <!-- Custom Back to Top button is used instead of scroll-to-top -->

    <!-- Smooth Scrolling Script -->
    <script src="{% static 'js/smooth-scroll.js' %}"></script>

    <!-- Include Cookie Consent Banner -->
    {% include 'cookie_consent_include.html' %}
</body>
</html>