# Generated by Django 5.2 on 2025-06-09 06:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_newslettersubscriber'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PasswordResetOTP',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contact_value', models.CharField(db_index=True, max_length=255)),
                ('contact_type', models.CharField(choices=[('email', 'Email'), ('phone', 'Phone')], max_length=10)),
                ('otp_code', models.CharField(max_length=6)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('is_used', models.BooleanField(default=False)),
                ('is_verified', models.BooleanField(default=False)),
                ('attempts', models.PositiveIntegerField(default=0)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Password Reset OTP',
                'verbose_name_plural': 'Password Reset OTPs',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['contact_value', 'contact_type'], name='accounts_pa_contact_5890d4_idx'), models.Index(fields=['otp_code', 'is_used'], name='accounts_pa_otp_cod_9fa708_idx'), models.Index(fields=['expires_at'], name='accounts_pa_expires_d8be21_idx')],
            },
        ),
    ]
