<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unsubscribe - WallpaperHub Newsletter</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        :root {
            --primary: #667eea;
            --primary-rgb: 102, 126, 234;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .unsubscribe-container {
            max-width: 500px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .unsubscribe-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: bold;
            color: white;
        }
        
        .btn-unsubscribe {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-unsubscribe:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(220, 53, 69, 0.3);
            color: white;
        }
        
        .btn-keep {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-keep:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="unsubscribe-container">
        <div class="unsubscribe-card">
            <div class="logo">WH</div>
            
            <h2 class="mb-4">Unsubscribe from Newsletter</h2>
            
            <p class="text-muted mb-4">
                We're sorry to see you go! Are you sure you want to unsubscribe from the WallpaperHub newsletter?
            </p>
            
            <div class="alert alert-info">
                <i class="bi bi-envelope me-2"></i>
                <strong>{{ subscriber.email }}</strong>
            </div>
            
            <p class="small text-muted mb-4">
                You'll no longer receive updates about new wallpapers, features, and exclusive content.
            </p>
            
            <form method="post" class="d-inline">
                {% csrf_token %}
                <button type="submit" class="btn btn-unsubscribe me-3">
                    <i class="bi bi-x-circle me-2"></i>
                    Yes, Unsubscribe
                </button>
            </form>
            
            <a href="/" class="btn btn-keep">
                <i class="bi bi-heart me-2"></i>
                Keep Me Subscribed
            </a>
            
            <hr class="my-4">
            
            <p class="small text-muted mb-0">
                <a href="/" class="text-decoration-none">Return to WallpaperHub</a>
            </p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
