<!DOCTYPE html>
{% load static %}
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - WallpaperHub</title>

    <!-- Favicon -->
    <link rel="icon" href="{% static 'favicon.svg' %}" type="image/svg+xml">
    <link rel="alternate icon" href="{% static 'favicon.ico' %}" type="image/x-icon">
    <link rel="shortcut icon" href="{% static 'favicon.ico' %}" type="image/x-icon">
    <link rel="apple-touch-icon" href="{% static 'apple-touch-icon.png' %}">
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Smooth Scroll CSS -->
    <link rel="stylesheet" href="{% static 'css/smooth-scroll.css' %}">

    <style>
        :root {
            --primary: #65558F;
            --primary-dark: #534979;
            --secondary: #F8F6FF;
            --dark: #333333;
            --light: #FFFFFF;
        }

        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            height: 100vh;
        }

        .auth-container {
            display: flex;
            height: 100vh;
        }

        .image-side {
            width: 50%;
            height: 105vh;
            position: relative;
            overflow: hidden;
            display: none;
        }

        @media (min-width: 992px) {
            .image-side {
                display: block;
            }
        }

        .image-slider {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            animation: slideImages 30s infinite;
        }

        .slider-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }

        .slider-image:nth-child(1) { animation: fadeInOut 15s 0s infinite; }
        .slider-image:nth-child(2) { animation: fadeInOut 15s 5s infinite; }
        .slider-image:nth-child(3) { animation: fadeInOut 15s 10s infinite; }

        @keyframes fadeInOut {
            0%, 45%, 100% { opacity: 0; }
            15%, 30% { opacity: 1; }
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, rgba(0,0,0,0.7), rgba(0,0,0,0.3));
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 2rem;
            color: white;
            z-index: 1;
        }

        .image-overlay h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .image-overlay p {
            font-size: 1.1rem;
            max-width: 80%;
            margin-bottom: 2rem;
        }

        .form-side {
            width: 100%;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light);
        }

        @media (min-width: 992px) {
            .form-side {
                width: 50%;
            }
        }

        .auth-form-container {
            max-width: 450px;
            margin: 0 auto;
            width: 100%;
        }

        /* Form styling */

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.25rem rgba(101, 85, 143, 0.25);
        }

        .password-field {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            top: 50%;
            right: 15px;
            transform: translateY(-50%);
            cursor: pointer;
            z-index: 10;
            background: none;
            border: none;
            color: #6c757d;
        }

        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .auth-footer {
            margin-top: 2rem;
            text-align: center;
        }

        .auth-footer a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }

        .auth-footer a:hover {
            text-decoration: underline;
        }

        .messages {
            margin-bottom: 1.5rem;
        }

        .alert {
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .password-requirements {
            background-color: #f8f9fa;
            padding: 0.75rem;
            border-radius: 0.5rem;
            border: 1px solid #e9ecef;
        }

        .password-requirements ul {
            margin-bottom: 0;
        }

        .password-strength {
            margin-top: 0.5rem;
        }

        .password-suggestion {
            background-color: #f8f9fa;
            padding: 0.75rem;
            border-radius: 0.5rem;
            border: 1px solid #e9ecef;
        }

        .is-valid {
            border-color: #198754 !important;
        }

        .is-invalid {
            border-color: #dc3545 !important;
        }
    </style>
</head>
<body>
    {% load static %}
    <div class="auth-container">
        <!-- Image Side with Slider -->
        <div class="image-side">
            <div class="image-slider">
                <img class="slider-image" src="https://images.unsplash.com/photo-1579546929518-9e396f3cc809?q=80&w=2070" alt="Abstract Wallpaper">
                <img class="slider-image" src="https://images.unsplash.com/photo-1493246507139-91e8fad9978e?q=80&w=2070" alt="Nature Wallpaper">
                <img class="slider-image" src="https://images.unsplash.com/photo-1472214103451-9374bd1c798e?q=80&w=2070" alt="Landscape Wallpaper">
            </div>
            <div class="image-overlay">
                <h2>Welcome to WallpaperHub</h2>
                <p>Join our community to access thousands of high-quality wallpapers for all your devices.</p>
                <a href="/landingPage" class="btn btn-outline-light px-4 py-2 rounded-pill">Learn More</a>
            </div>
        </div>

        <!-- Form Side -->
        <div class="form-side">
            <div class="auth-form-container">
                <!-- Logo and Heading Combined -->
                <div class="text-center mb-4">
                    <div class="d-flex justify-content-center align-items-center mb-3">
                        <img src="{% static 'Design6.jpg' %}" alt="WallpaperHub Logo" class="me-2" width="50" height="50" style="border-radius: 10px; margin-top: 25px;">
                        <span class="fs-2 fw-bold text-primary" style="margin-top: 25px;">WallpaperHub</span>
                    </div>
                    <h2 class="fw-bold">Create Account</h2>
                    <p class="text-muted">Sign up to get started with WallpaperHub</p>
                </div>

                <!-- Messages -->
                {% if messages %}
                <div class="messages">
                    {% for message in messages %}
                    <div class="alert {% if message.tags == 'error' %}alert-danger{% else %}alert-{{ message.tags }}{% endif %} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Sign Up Form -->
                <form method="POST" action="/signup/" onsubmit="return validateForm();">
                    {% csrf_token %}

                    <!-- Email Field -->
                    <div class="form-floating mb-3">
                        <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                        <label for="email">Email address</label>
                    </div>

                    <!-- Password Field -->
                    <div class="form-floating mb-2 password-field">
                        <input type="password" class="form-control" id="password" name="password" placeholder="Password" required oninput="checkPasswordStrength(); validatePassword()">
                        <label for="password">Password</label>
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <i class="bi bi-eye" id="password-icon"></i>
                        </button>
                    </div>

                    <!-- Password Requirements -->
                    <div class="password-requirements mb-3">
                        <p class="text-muted mb-2 small">Password must contain:</p>
                        <ul class="list-unstyled small">
                            <li class="mb-1 d-flex align-items-center">
                                <i class="bi bi-x-circle text-danger me-2" id="req-length"></i>
                                <span>At least 8 characters</span>
                            </li>
                            <li class="mb-1 d-flex align-items-center">
                                <i class="bi bi-x-circle text-danger me-2" id="req-uppercase"></i>
                                <span>At least one uppercase letter</span>
                            </li>
                            <li class="mb-1 d-flex align-items-center">
                                <i class="bi bi-x-circle text-danger me-2" id="req-special"></i>
                                <span>At least one special character</span>
                            </li>
                        </ul>
                    </div>

                    <!-- Password Strength Meter -->
                    <div class="password-strength mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="small">Password Strength:</span>
                            <span class="small" id="strength-text">None</span>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar" id="strength-meter" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>

                    <!-- Password Generate Button -->
                    <div class="mb-3 text-end">
                        <button type="button" class="btn btn-sm text-primary border-0 p-0" onclick="generatePassword()">
                            <i class="bi bi-magic"></i> Generate Strong Password
                        </button>
                    </div>

                    <!-- Confirm Password Field -->
                    <div class="form-floating mb-4 password-field">
                        <input type="password" class="form-control" id="confirm-password" name="confirm-password" placeholder="Confirm Password" required oninput="checkPasswordMatch()">
                        <label for="confirm-password">Confirm Password</label>
                        <button type="button" class="password-toggle" onclick="togglePassword('confirm-password')">
                            <i class="bi bi-eye" id="confirm-password-icon"></i>
                        </button>
                    </div>
                    <div class="invalid-feedback" id="password-match-feedback" style="display: none;">
                        Passwords do not match
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Create Account</button>
                    </div>
                </form>


                <!-- Footer Link -->
                <div class="auth-footer">
                    <p>Already have an account? <a href="/loginpage">Sign In</a></p>
                </div>

                <!-- Terms and Privacy -->
                <div class="mt-4 text-center">
                    <small class="text-muted">
                        By signing up, you agree to our
                        <a href="#" class="text-decoration-none">Terms of Service</a> and
                        <a href="#" class="text-decoration-none">Privacy Policy</a>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(inputId + '-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('bi-eye');
                toggleIcon.classList.add('bi-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('bi-eye-slash');
                toggleIcon.classList.add('bi-eye');
            }
        }

        function validatePassword() {
            const password = document.getElementById('password').value;
            const lengthReq = document.getElementById('req-length');
            const uppercaseReq = document.getElementById('req-uppercase');
            const specialReq = document.getElementById('req-special');

            // Check length requirement (at least 8 characters)
            if (password.length >= 8) {
                lengthReq.classList.remove('bi-x-circle', 'text-danger');
                lengthReq.classList.add('bi-check-circle', 'text-success');
            } else {
                lengthReq.classList.remove('bi-check-circle', 'text-success');
                lengthReq.classList.add('bi-x-circle', 'text-danger');
            }

            // Check uppercase requirement
            if (/[A-Z]/.test(password)) {
                uppercaseReq.classList.remove('bi-x-circle', 'text-danger');
                uppercaseReq.classList.add('bi-check-circle', 'text-success');
            } else {
                uppercaseReq.classList.remove('bi-check-circle', 'text-success');
                uppercaseReq.classList.add('bi-x-circle', 'text-danger');
            }

            // Check special character requirement
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
                specialReq.classList.remove('bi-x-circle', 'text-danger');
                specialReq.classList.add('bi-check-circle', 'text-success');
            } else {
                specialReq.classList.remove('bi-check-circle', 'text-success');
                specialReq.classList.add('bi-x-circle', 'text-danger');
            }
        }

        function checkPasswordStrength() {
            const password = document.getElementById('password').value;
            const strengthMeter = document.getElementById('strength-meter');
            const strengthText = document.getElementById('strength-text');

            // If password is empty, reset meter
            if (password.length === 0) {
                strengthMeter.style.width = '0%';
                strengthMeter.className = 'progress-bar';
                strengthText.textContent = 'None';
                return;
            }

            let strength = 0;

            // Length contribution (up to 25%)
            strength += Math.min(password.length * 2.5, 25);

            // Complexity contribution
            if (/[A-Z]/.test(password)) strength += 15; // Uppercase
            if (/[a-z]/.test(password)) strength += 10; // Lowercase
            if (/[0-9]/.test(password)) strength += 15; // Numbers
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 20; // Special chars

            // Variety contribution
            const uniqueChars = new Set(password).size;
            strength += Math.min(uniqueChars * 2, 15);

            // Set the strength meter
            strengthMeter.style.width = strength + '%';

            // Update color and text based on strength
            if (strength < 30) {
                strengthMeter.className = 'progress-bar bg-danger';
                strengthText.textContent = 'Weak';
                strengthText.className = 'small text-danger';
            } else if (strength < 60) {
                strengthMeter.className = 'progress-bar bg-warning';
                strengthText.textContent = 'Medium';
                strengthText.className = 'small text-warning';
            } else if (strength < 80) {
                strengthMeter.className = 'progress-bar bg-info';
                strengthText.textContent = 'Strong';
                strengthText.className = 'small text-info';
            } else {
                strengthMeter.className = 'progress-bar bg-success';
                strengthText.textContent = 'Very Strong';
                strengthText.className = 'small text-success';
            }

            // No password suggestion needed
        }

        // Removed password suggestion function

        function generatePassword() {
            // Define character sets
            const uppercaseChars = 'ABCDEFGHJKLMNPQRSTUVWXYZ'; // Removed confusing chars like I, O
            const lowercaseChars = 'abcdefghijkmnopqrstuvwxyz'; // Removed confusing chars like l
            const numberChars = '23456789'; // Removed confusing chars like 0, 1
            const specialChars = '!@#$%^&*-_=+?';

            // Generate a random password with good distribution of character types
            let password = '';

            // Ensure at least one of each required type
            password += uppercaseChars.charAt(Math.floor(Math.random() * uppercaseChars.length));
            password += lowercaseChars.charAt(Math.floor(Math.random() * lowercaseChars.length));
            password += numberChars.charAt(Math.floor(Math.random() * numberChars.length));
            password += specialChars.charAt(Math.floor(Math.random() * specialChars.length));

            // Add more random characters to reach desired length (12 chars total)
            const allChars = uppercaseChars + lowercaseChars + numberChars + specialChars;
            for (let i = 0; i < 8; i++) {
                password += allChars.charAt(Math.floor(Math.random() * allChars.length));
            }

            // Shuffle the password characters
            password = password.split('').sort(() => 0.5 - Math.random()).join('');

            // Set the generated password
            document.getElementById('password').value = password;
            document.getElementById('confirm-password').value = password;

            // Update validation and strength
            validatePassword();
            checkPasswordStrength();
            checkPasswordMatch();
        }

        function checkPasswordMatch() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            const feedback = document.getElementById('password-match-feedback');
            const confirmInput = document.getElementById('confirm-password');

            if (confirmPassword === '') {
                feedback.style.display = 'none';
                confirmInput.classList.remove('is-invalid', 'is-valid');
            } else if (password === confirmPassword) {
                feedback.style.display = 'none';
                confirmInput.classList.remove('is-invalid');
                confirmInput.classList.add('is-valid');
            } else {
                feedback.style.display = 'block';
                confirmInput.classList.remove('is-valid');
                confirmInput.classList.add('is-invalid');
            }
        }

        // Form validation before submission
        function validateForm() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            const email = document.getElementById('email').value;

            // Check if email is valid
            if (!email || !email.includes('@') || !email.includes('.')) {
                showFormError('Please enter a valid email address.');
                return false;
            }

            // Check if passwords match
            if (password !== confirmPassword) {
                showFormError('Passwords do not match!');
                return false;
            }

            // Check password requirements
            const hasUppercase = /[A-Z]/.test(password);
            const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
            const hasMinLength = password.length >= 8;

            if (!hasUppercase || !hasSpecialChar || !hasMinLength) {
                showFormError('Password must contain at least 8 characters, one uppercase letter, and one special character!');
                return false;
            }

            // Show loading state
            const submitBtn = document.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Creating Account...';
            submitBtn.disabled = true;

            return true;
        }

        // Helper function to show form errors
        function showFormError(message) {
            // Create a Bootstrap alert
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            // Add to messages container
            const messagesContainer = document.querySelector('.messages');
            if (!messagesContainer) {
                // Create messages container if it doesn't exist
                const newMessagesContainer = document.createElement('div');
                newMessagesContainer.className = 'messages';
                newMessagesContainer.appendChild(alertDiv);

                // Insert before the form
                const form = document.querySelector('form');
                form.parentNode.insertBefore(newMessagesContainer, form);
            } else {
                // Clear existing messages and add new one
                messagesContainer.innerHTML = '';
                messagesContainer.appendChild(alertDiv);
            }
        }

        // Initialize validation on page load
        document.addEventListener('DOMContentLoaded', function() {
            validatePassword();
            checkPasswordStrength();

            // Auto-hide success messages after 5 seconds, keep error messages visible
            const messages = document.querySelectorAll('.alert');
            if (messages.length > 0) {
                messages.forEach(function(message) {
                    // Only auto-hide success messages
                    if (message.classList.contains('alert-success')) {
                        setTimeout(function() {
                            // Use Bootstrap's fade out
                            message.classList.remove('show');
                            setTimeout(() => {
                                message.remove();
                            }, 150);
                        }, 5000);
                    }

                    // Add click handler for close button
                    const closeBtn = message.querySelector('.btn-close');
                    if (closeBtn) {
                        closeBtn.addEventListener('click', function() {
                            message.classList.remove('show');
                            setTimeout(() => {
                                message.remove();
                            }, 150);
                        });
                    }
                });
            }
        });
    </script>

    <!-- Smooth Scrolling Script -->
    <script src="{% static 'js/smooth-scroll.js' %}"></script>

    <!-- Include Cookie Consent Banner -->
    {% include 'cookie_consent_include.html' %}
</body>
</html>