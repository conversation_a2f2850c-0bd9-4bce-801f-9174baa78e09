<!DOCTYPE html>
{% load static %}
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Sign Up - WallpaperHub</title>
    <meta name="description" content="Create your WallpaperHub account to access thousands of high-quality wallpapers">
    <meta name="theme-color" content="#65558F">

    <!-- Favicon -->
    <link rel="icon" href="{% static 'favicon.svg' %}" type="image/svg+xml">
    <link rel="alternate icon" href="{% static 'favicon.ico' %}" type="image/x-icon">
    <link rel="shortcut icon" href="{% static 'favicon.ico' %}" type="image/x-icon">
    <link rel="apple-touch-icon" href="{% static 'apple-touch-icon.png' %}">
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Smooth Scroll CSS -->
    <link rel="stylesheet" href="{% static 'css/smooth-scroll.css' %}">

    <style>
        :root {
            --primary: #65558F;
            --primary-dark: #534979;
            --primary-light: #8B7BB8;
            --secondary: #F8F6FF;
            --accent: #FF6B6B;
            --success: #4ECDC4;
            --warning: #FFE66D;
            --dark: #2C3E50;
            --light: #FFFFFF;
            --gray-100: #F8F9FA;
            --gray-200: #E9ECEF;
            --gray-300: #DEE2E6;
            --gray-400: #CED4DA;
            --gray-500: #ADB5BD;
            --gray-600: #6C757D;
            --gray-700: #495057;
            --gray-800: #343A40;
            --gray-900: #212529;
            --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
            --border-radius: 0.75rem;
            --border-radius-sm: 0.5rem;
            --border-radius-lg: 1rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255,255,255,0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        .auth-container {
            display: flex;
            min-height: 100vh;
            position: relative;
            z-index: 1;
        }

        .image-side {
            width: 50%;
            position: relative;
            overflow: hidden;
            display: none;
        }

        @media (min-width: 992px) {
            .image-side {
                display: block;
            }
        }

        .image-slider {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .slider-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0;
            transition: opacity 2s ease-in-out;
        }

        .slider-image:nth-child(1) { animation: fadeInOut 18s 0s infinite; }
        .slider-image:nth-child(2) { animation: fadeInOut 18s 6s infinite; }
        .slider-image:nth-child(3) { animation: fadeInOut 18s 12s infinite; }

        @keyframes fadeInOut {
            0%, 33.33%, 100% { opacity: 0; }
            16.66%, 27.77% { opacity: 1; }
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(101, 85, 143, 0.9) 0%, rgba(83, 73, 121, 0.8) 50%, rgba(0,0,0,0.6) 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 3rem 2rem;
            color: white;
            z-index: 1;
        }

        .image-overlay h2 {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            line-height: 1.2;
        }

        .image-overlay p {
            font-size: clamp(1rem, 2vw, 1.25rem);
            max-width: 85%;
            margin-bottom: 2.5rem;
            line-height: 1.6;
            opacity: 0.95;
        }

        .image-overlay .btn {
            align-self: flex-start;
            padding: 0.75rem 2rem;
            font-weight: 600;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .image-overlay .btn:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-2px);
        }

        .form-side {
            width: 100%;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: var(--light);
            position: relative;
            overflow-y: auto;
            min-height: 100vh;
        }

        @media (min-width: 576px) {
            .form-side {
                padding: 2rem;
            }
        }

        @media (min-width: 992px) {
            .form-side {
                width: 50%;
                min-height: 100vh;
            }
        }

        .auth-form-container {
            max-width: 480px;
            margin: 0 auto;
            width: 100%;
            padding: 1.5rem 0;
            position: relative;
        }

        @media (min-width: 576px) {
            .auth-form-container {
                padding: 2rem 0;
            }
        }

        /* Enhanced logo and text spacing */
        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-bottom: 2rem;
            padding: 1rem 0;
        }

        .logo-container img {
            flex-shrink: 0;
            box-shadow: var(--shadow-sm);
            transition: transform 0.3s ease;
        }

        .logo-container img:hover {
            transform: scale(1.05);
        }

        .logo-container span {
            white-space: nowrap;
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
        }

        .auth-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .auth-header h2 {
            color: var(--dark);
            font-weight: 700;
            margin-bottom: 0.5rem;
            font-size: clamp(1.75rem, 4vw, 2.25rem);
        }

        .auth-header p {
            color: var(--gray-600);
            font-size: clamp(0.9rem, 2vw, 1rem);
            margin: 0;
        }

        /* Enhanced Form styling */
        .form-floating {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-control {
            border: 2px solid var(--gray-300);
            border-radius: var(--border-radius-sm);
            padding: 1rem 0.75rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--light);
            height: auto;
            min-height: 3.5rem;
        }

        .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.25rem rgba(101, 85, 143, 0.15);
            background: var(--light);
            outline: none;
        }

        .form-control:valid {
            border-color: var(--success);
        }

        .form-control.is-invalid {
            border-color: var(--accent);
            box-shadow: 0 0 0 0.25rem rgba(255, 107, 107, 0.15);
        }

        .form-control.is-valid {
            border-color: var(--success);
            box-shadow: 0 0 0 0.25rem rgba(78, 205, 196, 0.15);
        }

        .form-floating > label {
            color: var(--gray-600);
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            color: var(--primary);
            font-weight: 600;
        }

        .password-field {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            top: 50%;
            right: 15px;
            transform: translateY(-50%);
            cursor: pointer;
            z-index: 10;
            background: none;
            border: none;
            color: var(--gray-500);
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .password-toggle:hover {
            color: var(--primary);
            background: var(--gray-100);
        }

        /* Responsive form rows */
        .form-row {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-row .form-floating {
            flex: 1;
            margin-bottom: 0;
        }

        @media (max-width: 575px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }

            .form-row .form-floating {
                margin-bottom: 1.5rem;
            }
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            border: none;
            padding: 1rem 2rem;
            font-weight: 600;
            border-radius: var(--border-radius-sm);
            transition: all 0.3s ease;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover, .btn-primary:focus {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:active {
            transform: translateY(-1px);
        }

        .auth-footer {
            margin-top: 2.5rem;
            text-align: center;
            padding: 1rem 0;
        }

        .auth-footer p {
            color: var(--gray-600);
            margin-bottom: 0;
            font-size: 0.95rem;
        }

        .auth-footer a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .auth-footer a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        .messages {
            margin-bottom: 2rem;
        }

        .alert {
            padding: 1rem 1.25rem;
            border-radius: var(--border-radius-sm);
            margin-bottom: 1rem;
            border: none;
            box-shadow: var(--shadow-sm);
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, var(--success), #45B7B8);
            color: white;
        }

        .alert-danger {
            background: linear-gradient(135deg, var(--accent), #FF5252);
            color: white;
        }

        .password-requirements {
            background: var(--gray-100);
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            border: 2px solid var(--gray-200);
            margin-bottom: 1rem;
        }

        .password-requirements ul {
            margin-bottom: 0;
        }

        .password-requirements li {
            transition: all 0.3s ease;
        }

        .password-strength {
            margin-bottom: 1rem;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
            background: var(--gray-200);
            overflow: hidden;
        }

        .progress-bar {
            transition: all 0.3s ease;
            border-radius: 4px;
        }

        .form-text {
            margin-top: 1px;
        }

        .form-text small {
            font-size: 0.875rem;
            /* line-height: 1.4; */
        }

        /* Enhanced validation styles */
        .is-valid {
            border-color: var(--success) !important;
        }

        .is-invalid {
            border-color: var(--accent) !important;
        }

        /* Mobile optimizations */
        @media (max-width: 575px) {
            .auth-container {
                flex-direction: column;
            }

            .image-side {
                display: none;
            }

            .form-side {
                width: 100%;
                padding: 1rem;
                min-height: 100vh;
            }

            .auth-form-container {
                padding: 1rem 0;
            }

            .logo-container {
                margin-bottom: 1.5rem;
                gap: 10px;
            }

            .logo-container img {
                width: 40px;
                height: 40px;
            }

            .logo-container span {
                font-size: 1.5rem;
            }

            .auth-header h2 {
                font-size: 1.75rem;
            }

            .password-requirements {
                padding: 0.75rem;
            }
        }

        /* Tablet optimizations */
        @media (min-width: 576px) and (max-width: 991px) {
            .image-side {
                display: none;
            }

            .form-side {
                width: 100%;
                padding: 2rem;
                background: linear-gradient(135deg, var(--light) 0%, var(--gray-100) 100%);
            }

            .auth-form-container {
                max-width: 500px;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                padding: 3rem 2rem;
                border-radius: var(--border-radius-lg);
                box-shadow: var(--shadow-lg);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        }

        /* Glass morphism effect for mobile */
        @media (max-width: 575px) {
            .form-side {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
            }

            .auth-form-container {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: var(--border-radius-lg);
                margin: 1rem;
                padding: 2rem 1.5rem;
                box-shadow: var(--shadow-lg);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        }

        /* Large screen optimizations */
        @media (min-width: 1200px) {
            .auth-form-container {
                max-width: 520px;
                padding: 3rem 0;
            }

            .image-overlay {
                padding: 4rem 3rem;
            }
        }

        /* Animation for form elements */
        .auth-form-container > * {
            animation: slideInUp 0.6s ease-out both;
        }

        .logo-container { animation-delay: 0.1s; }
        .auth-header { animation-delay: 0.2s; }
        .form-row:nth-of-type(1) { animation-delay: 0.3s; }
        .form-floating:nth-of-type(1) { animation-delay: 0.4s; }
        .form-floating:nth-of-type(2) { animation-delay: 0.5s; }
        .form-floating:nth-of-type(3) { animation-delay: 0.6s; }
        .form-floating:nth-of-type(4) { animation-delay: 0.7s; }
        .password-requirements { animation-delay: 0.8s; }
        .password-strength { animation-delay: 0.9s; }
        .btn-primary { animation-delay: 1.0s; }
        .auth-footer { animation-delay: 1.1s; }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Hover effects for form inputs */
        .form-control:hover:not(:focus) {
            border-color: var(--primary-light);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(101, 85, 143, 0.1);
        }

        /* Focus ring animation */
        .form-control:focus {
            animation: focusRing 0.3s ease-out;
        }

        @keyframes focusRing {
            0% {
                box-shadow: 0 0 0 0 rgba(101, 85, 143, 0.4);
            }
            100% {
                box-shadow: 0 0 0 0.25rem rgba(101, 85, 143, 0.15);
            }
        }

        /* Pulse animation for submit button */
        .btn-primary:not(:disabled):hover {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(101, 85, 143, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(101, 85, 143, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(101, 85, 143, 0);
            }
        }

        /* Loading state */
        .btn-loading {
            position: relative;
            color: transparent;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid transparent;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    {% load static %}
    <div class="auth-container">
        <!-- Image Side with Slider -->
        <div class="image-side">
            <div class="image-slider">
                <img class="slider-image" src="https://images.unsplash.com/photo-1579546929518-9e396f3cc809?q=80&w=2070" alt="Abstract Wallpaper">
                <img class="slider-image" src="https://images.unsplash.com/photo-1493246507139-91e8fad9978e?q=80&w=2070" alt="Nature Wallpaper">
                <img class="slider-image" src="https://images.unsplash.com/photo-1472214103451-9374bd1c798e?q=80&w=2070" alt="Landscape Wallpaper">
            </div>
            <div class="image-overlay">
                <h2>Welcome to WallpaperHub</h2>
                <p>Join our community to access thousands of high-quality wallpapers for all your devices.</p>
                <a href="/landingPage" class="btn btn-outline-light px-4 py-2 rounded-pill">Learn More</a>
            </div>
        </div>

        <!-- Form Side -->
        <div class="form-side">
            <div class="auth-form-container">
                <!-- Logo and Heading Combined -->
                <div class="logo-container">
                    <img src="{% static 'Design6.jpg' %}" alt="WallpaperHub Logo" width="45" height="45" style="border-radius: 8px;">
                    <span class="fs-2 fw-bold" style="line-height: 1.2;">WallpaperHub</span>
                </div>

                <div class="auth-header">
                    <h2>Create Account</h2>
                    <p>Sign up to get started with WallpaperHub</p>
                </div>

                <!-- Messages -->
                {% if messages %}
                <div class="messages">
                    {% for message in messages %}
                    <div class="alert {% if message.tags == 'error' %}alert-danger{% else %}alert-{{ message.tags }}{% endif %} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Sign Up Form -->
                <form method="POST" action="/signup/" onsubmit="return validateForm();">
                    {% csrf_token %}

                    <!-- Name Fields Row -->
                    <div class="form-row">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="first_name" name="first_name" placeholder="First Name">
                            <label for="first_name">First Name</label>
                        </div>
                        <div class="form-floating">
                            <input type="text" class="form-control" id="last_name" name="last_name" placeholder="Last Name">
                            <label for="last_name">Last Name</label>
                        </div>
                    </div>

                    <!-- Email Field -->
                    <div class="form-floating mb-3">
                        <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                        <label for="email">Email address</label>
                    </div>

                    <!-- Phone Number Field -->
                    <div class="form-floating">
                        <input type="tel" class="form-control" id="phone_number" name="phone_number" placeholder="+91 98765 43210" oninput="formatPhoneNumber(this)">
                        <label for="phone_number">Phone Number (Optional)</label>
                    </div>
                    <div class="form-text">
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            Enter Indian mobile for account recovery.
                        </small>
                    </div>

                    <!-- Password Field -->
                    <div class="form-floating mb-2 password-field">
                        <input type="password" class="form-control" id="password" name="password" placeholder="Password" required oninput="checkPasswordStrength(); validatePassword()">
                        <label for="password">Password</label>
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <i class="bi bi-eye" id="password-icon"></i>
                        </button>
                    </div>

                    <!-- Password Requirements -->
                    <div class="password-requirements mb-3">
                        <p class="text-muted mb-2 small">Password must contain:</p>
                        <ul class="list-unstyled small">
                            <li class="mb-1 d-flex align-items-center">
                                <i class="bi bi-x-circle text-danger me-2" id="req-length"></i>
                                <span>At least 8 characters</span>
                            </li>
                            <li class="mb-1 d-flex align-items-center">
                                <i class="bi bi-x-circle text-danger me-2" id="req-uppercase"></i>
                                <span>At least one uppercase letter</span>
                            </li>
                            <li class="mb-1 d-flex align-items-center">
                                <i class="bi bi-x-circle text-danger me-2" id="req-special"></i>
                                <span>At least one special character</span>
                            </li>
                        </ul>
                    </div>

                    <!-- Password Strength Meter -->
                    <div class="password-strength mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="small">Password Strength:</span>
                            <span class="small" id="strength-text">None</span>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar" id="strength-meter" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>

                    <!-- Password Generate Button -->
                    <div class="mb-3 text-end">
                        <button type="button" class="btn btn-sm text-primary border-0 p-0" onclick="generatePassword()">
                            <i class="bi bi-magic"></i> Generate Strong Password
                        </button>
                    </div>

                    <!-- Confirm Password Field -->
                    <div class="form-floating mb-4 password-field">
                        <input type="password" class="form-control" id="confirm-password" name="confirm-password" placeholder="Confirm Password" required oninput="checkPasswordMatch()">
                        <label for="confirm-password">Confirm Password</label>
                        <button type="button" class="password-toggle" onclick="togglePassword('confirm-password')">
                            <i class="bi bi-eye" id="confirm-password-icon"></i>
                        </button>
                    </div>
                    <div class="invalid-feedback" id="password-match-feedback" style="display: none;">
                        Passwords do not match
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Create Account</button>
                    </div>
                </form>


                <!-- Footer Link -->
                <div class="auth-footer">
                    <p>Already have an account? <a href="/loginpage">Sign In</a></p>
                </div>

                <!-- Terms and Privacy -->
                <div class="mt-4 text-center">
                    <small class="text-muted">
                        By signing up, you agree to our
                        <a href="#" class="text-decoration-none text-primary">Terms of Service</a> and
                        <a href="#" class="text-decoration-none text-primary">Privacy Policy</a>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(inputId + '-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('bi-eye');
                toggleIcon.classList.add('bi-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('bi-eye-slash');
                toggleIcon.classList.add('bi-eye');
            }
        }

        function validatePassword() {
            const password = document.getElementById('password').value;
            const lengthReq = document.getElementById('req-length');
            const uppercaseReq = document.getElementById('req-uppercase');
            const specialReq = document.getElementById('req-special');

            // Check length requirement (at least 8 characters)
            if (password.length >= 8) {
                lengthReq.classList.remove('bi-x-circle', 'text-danger');
                lengthReq.classList.add('bi-check-circle', 'text-success');
            } else {
                lengthReq.classList.remove('bi-check-circle', 'text-success');
                lengthReq.classList.add('bi-x-circle', 'text-danger');
            }

            // Check uppercase requirement
            if (/[A-Z]/.test(password)) {
                uppercaseReq.classList.remove('bi-x-circle', 'text-danger');
                uppercaseReq.classList.add('bi-check-circle', 'text-success');
            } else {
                uppercaseReq.classList.remove('bi-check-circle', 'text-success');
                uppercaseReq.classList.add('bi-x-circle', 'text-danger');
            }

            // Check special character requirement
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
                specialReq.classList.remove('bi-x-circle', 'text-danger');
                specialReq.classList.add('bi-check-circle', 'text-success');
            } else {
                specialReq.classList.remove('bi-check-circle', 'text-success');
                specialReq.classList.add('bi-x-circle', 'text-danger');
            }
        }

        function checkPasswordStrength() {
            const password = document.getElementById('password').value;
            const strengthMeter = document.getElementById('strength-meter');
            const strengthText = document.getElementById('strength-text');

            // If password is empty, reset meter
            if (password.length === 0) {
                strengthMeter.style.width = '0%';
                strengthMeter.className = 'progress-bar';
                strengthText.textContent = 'None';
                return;
            }

            let strength = 0;

            // Length contribution (up to 25%)
            strength += Math.min(password.length * 2.5, 25);

            // Complexity contribution
            if (/[A-Z]/.test(password)) strength += 15; // Uppercase
            if (/[a-z]/.test(password)) strength += 10; // Lowercase
            if (/[0-9]/.test(password)) strength += 15; // Numbers
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 20; // Special chars

            // Variety contribution
            const uniqueChars = new Set(password).size;
            strength += Math.min(uniqueChars * 2, 15);

            // Set the strength meter
            strengthMeter.style.width = strength + '%';

            // Update color and text based on strength
            if (strength < 30) {
                strengthMeter.className = 'progress-bar bg-danger';
                strengthText.textContent = 'Weak';
                strengthText.className = 'small text-danger';
            } else if (strength < 60) {
                strengthMeter.className = 'progress-bar bg-warning';
                strengthText.textContent = 'Medium';
                strengthText.className = 'small text-warning';
            } else if (strength < 80) {
                strengthMeter.className = 'progress-bar bg-info';
                strengthText.textContent = 'Strong';
                strengthText.className = 'small text-info';
            } else {
                strengthMeter.className = 'progress-bar bg-success';
                strengthText.textContent = 'Very Strong';
                strengthText.className = 'small text-success';
            }

            // No password suggestion needed
        }

        // Removed password suggestion function

        function generatePassword() {
            // Define character sets
            const uppercaseChars = 'ABCDEFGHJKLMNPQRSTUVWXYZ'; // Removed confusing chars like I, O
            const lowercaseChars = 'abcdefghijkmnopqrstuvwxyz'; // Removed confusing chars like l
            const numberChars = '23456789'; // Removed confusing chars like 0, 1
            const specialChars = '!@#$%^&*-_=+?';

            // Generate a random password with good distribution of character types
            let password = '';

            // Ensure at least one of each required type
            password += uppercaseChars.charAt(Math.floor(Math.random() * uppercaseChars.length));
            password += lowercaseChars.charAt(Math.floor(Math.random() * lowercaseChars.length));
            password += numberChars.charAt(Math.floor(Math.random() * numberChars.length));
            password += specialChars.charAt(Math.floor(Math.random() * specialChars.length));

            // Add more random characters to reach desired length (12 chars total)
            const allChars = uppercaseChars + lowercaseChars + numberChars + specialChars;
            for (let i = 0; i < 8; i++) {
                password += allChars.charAt(Math.floor(Math.random() * allChars.length));
            }

            // Shuffle the password characters
            password = password.split('').sort(() => 0.5 - Math.random()).join('');

            // Set the generated password
            document.getElementById('password').value = password;
            document.getElementById('confirm-password').value = password;

            // Update validation and strength
            validatePassword();
            checkPasswordStrength();
            checkPasswordMatch();
        }

        function checkPasswordMatch() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            const feedback = document.getElementById('password-match-feedback');
            const confirmInput = document.getElementById('confirm-password');

            if (confirmPassword === '') {
                feedback.style.display = 'none';
                confirmInput.classList.remove('is-invalid', 'is-valid');
            } else if (password === confirmPassword) {
                feedback.style.display = 'none';
                confirmInput.classList.remove('is-invalid');
                confirmInput.classList.add('is-valid');
            } else {
                feedback.style.display = 'block';
                confirmInput.classList.remove('is-valid');
                confirmInput.classList.add('is-invalid');
            }
        }

        // Phone number formatting for Indian numbers
        function formatPhoneNumber(input) {
            // Remove all non-digit characters
            let value = input.value.replace(/\D/g, '');

            // Handle Indian mobile numbers
            if (value.length > 0) {
                // If starts with 91, it's already with country code
                if (value.startsWith('91') && value.length <= 12) {
                    // Format: +91 XXXXX XXXXX
                    if (value.length >= 12) {
                        value = '+91 ' + value.substring(2, 7) + ' ' + value.substring(7, 12);
                    } else if (value.length >= 7) {
                        value = '+91 ' + value.substring(2, 7) + ' ' + value.substring(7);
                    } else if (value.length >= 2) {
                        value = '+91 ' + value.substring(2);
                    }
                }
                // If 10 digits without country code, add +91
                else if (value.length === 10 && (value.startsWith('6') || value.startsWith('7') || value.startsWith('8') || value.startsWith('9'))) {
                    value = '+91 ' + value.substring(0, 5) + ' ' + value.substring(5);
                }
                // If more than 10 digits and doesn't start with 91, assume it needs +91
                else if (value.length === 10) {
                    value = '+91 ' + value.substring(0, 5) + ' ' + value.substring(5);
                }
                // Partial formatting
                else if (value.length < 10 && value.length > 0) {
                    if (value.startsWith('91')) {
                        if (value.length > 7) {
                            value = '+91 ' + value.substring(2, 7) + ' ' + value.substring(7);
                        } else if (value.length > 2) {
                            value = '+91 ' + value.substring(2);
                        } else {
                            value = '+' + value;
                        }
                    } else {
                        if (value.length > 5) {
                            value = '+91 ' + value.substring(0, 5) + ' ' + value.substring(5);
                        } else {
                            value = '+91 ' + value;
                        }
                    }
                }
                // Handle other country codes or longer numbers
                else if (value.length > 12) {
                    // Truncate to max length
                    value = value.substring(0, 12);
                    value = '+91 ' + value.substring(2, 7) + ' ' + value.substring(7, 12);
                }
            }

            input.value = value;

            // Validate phone number
            validatePhoneNumber(input);
        }

        function validatePhoneNumber(input) {
            // Indian mobile number pattern: +91 XXXXX XXXXX
            const indianPhonePattern = /^\+91 [6-9]\d{4} \d{5}$/;
            const isValid = indianPhonePattern.test(input.value) || input.value === '';

            if (input.value && !isValid) {
                input.classList.add('is-invalid');
                input.classList.remove('is-valid');
                // Show helper text for invalid format
                let helpText = input.parentElement.nextElementSibling;
                if (helpText && helpText.classList.contains('form-text')) {
                    helpText.innerHTML = '<small class="text-danger"><i class="bi bi-exclamation-triangle me-1"></i>Please enter a valid Indian mobile number (e.g., +91 98765 43210)</small>';
                }
            } else if (input.value && isValid) {
                input.classList.add('is-valid');
                input.classList.remove('is-invalid');
                // Restore original helper text
                let helpText = input.parentElement.nextElementSibling;
                if (helpText && helpText.classList.contains('form-text')) {
                    helpText.innerHTML = '<small class="text-muted"><i class="bi bi-info-circle me-1"></i>Enter  mobile number for account recovery</small>';
                }
            } else {
                input.classList.remove('is-invalid', 'is-valid');
                // Restore original helper text
                let helpText = input.parentElement.nextElementSibling;
                if (helpText && helpText.classList.contains('form-text')) {
                    helpText.innerHTML = '<small class="text-muted"><i class="bi bi-info-circle me-1"></i>Enter  mobile number for account recovery</small>';
                }
            }
        }

        // Form validation before submission
        function validateForm() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            const email = document.getElementById('email').value;
            const phoneNumber = document.getElementById('phone_number').value;

            // Check if email is valid
            if (!email || !email.includes('@') || !email.includes('.')) {
                showFormError('Please enter a valid email address.');
                return false;
            }

            // Check phone number if provided
            if (phoneNumber) {
                const indianPhonePattern = /^\+91 [6-9]\d{4} \d{5}$/;
                if (!indianPhonePattern.test(phoneNumber)) {
                    showFormError('Please enter a valid Indian mobile number in the format +91 XXXXX XXXXX.');
                    return false;
                }
            }

            // Check if passwords match
            if (password !== confirmPassword) {
                showFormError('Passwords do not match!');
                return false;
            }

            // Check password requirements
            const hasUppercase = /[A-Z]/.test(password);
            const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
            const hasMinLength = password.length >= 8;

            if (!hasUppercase || !hasSpecialChar || !hasMinLength) {
                showFormError('Password must contain at least 8 characters, one uppercase letter, and one special character!');
                return false;
            }

            // Show loading state
            const submitBtn = document.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Creating Account...';
            submitBtn.disabled = true;

            return true;
        }

        // Helper function to show form errors
        function showFormError(message) {
            // Create a Bootstrap alert
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            // Add to messages container
            const messagesContainer = document.querySelector('.messages');
            if (!messagesContainer) {
                // Create messages container if it doesn't exist
                const newMessagesContainer = document.createElement('div');
                newMessagesContainer.className = 'messages';
                newMessagesContainer.appendChild(alertDiv);

                // Insert before the form
                const form = document.querySelector('form');
                form.parentNode.insertBefore(newMessagesContainer, form);
            } else {
                // Clear existing messages and add new one
                messagesContainer.innerHTML = '';
                messagesContainer.appendChild(alertDiv);
            }
        }

        // Initialize validation on page load
        document.addEventListener('DOMContentLoaded', function() {
            validatePassword();
            checkPasswordStrength();

            // Auto-hide success messages after 5 seconds, keep error messages visible
            const messages = document.querySelectorAll('.alert');
            if (messages.length > 0) {
                messages.forEach(function(message) {
                    // Only auto-hide success messages
                    if (message.classList.contains('alert-success')) {
                        setTimeout(function() {
                            // Use Bootstrap's fade out
                            message.classList.remove('show');
                            setTimeout(() => {
                                message.remove();
                            }, 150);
                        }, 5000);
                    }

                    // Add click handler for close button
                    const closeBtn = message.querySelector('.btn-close');
                    if (closeBtn) {
                        closeBtn.addEventListener('click', function() {
                            message.classList.remove('show');
                            setTimeout(() => {
                                message.remove();
                            }, 150);
                        });
                    }
                });
            }
        });
    </script>

    <!-- Smooth Scrolling Script -->
    <script src="{% static 'js/smooth-scroll.js' %}"></script>

    <!-- Include Cookie Consent Banner -->
    {% include 'cookie_consent_include.html' %}
</body>
</html>