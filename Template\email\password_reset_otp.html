<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Code - WallpaperHub</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #0E121B 0%, #667eea 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background-color: white;
            border-radius: 12px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: #0E121B;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .security-notice {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .security-notice h3 {
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .otp-section {
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: linear-gradient(135deg, #0E121B 0%, #667eea 100%);
            border-radius: 12px;
            color: white;
        }
        
        .otp-section h2 {
            font-size: 24px;
            margin-bottom: 20px;
        }
        
        .otp-code {
            font-size: 48px;
            font-weight: bold;
            letter-spacing: 8px;
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border: 2px dashed rgba(255, 255, 255, 0.3);
        }
        
        .expiry-info {
            font-size: 16px;
            opacity: 0.9;
            margin-top: 15px;
        }
        
        .instructions {
            background-color: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin: 30px 0;
        }
        
        .instructions h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .instructions ol {
            color: #666;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .warning {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
            border-left: 4px solid #e17055;
        }
        
        .warning h4 {
            color: #2d3436;
            margin-bottom: 10px;
        }
        
        .warning p {
            color: #636e72;
            margin: 0;
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .footer p {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .footer a {
            color: #0E121B;
            text-decoration: none;
            font-weight: 600;
        }
        
        /* Mobile responsiveness */
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .header, .content, .footer {
                padding: 20px;
            }
            
            .otp-code {
                font-size: 36px;
                letter-spacing: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">WH</div>
            <h1>Password Reset Request</h1>
            <p>Secure verification code for your account</p>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="security-notice">
                <h3>🔐 Security Alert</h3>
                <p>A password reset was requested for your WallpaperHub account</p>
            </div>
            
            <p>Hello,</p>
            
            <p>We received a request to reset the password for your WallpaperHub account associated with <strong>{{ contact_value }}</strong>.</p>
            
            <div class="otp-section">
                <h2>Your Verification Code</h2>
                <div class="otp-code">{{ otp_code }}</div>
                <div class="expiry-info">
                    ⏰ This code expires in {{ expires_minutes }} minutes
                </div>
            </div>
            
            <div class="instructions">
                <h3>How to use this code:</h3>
                <ol>
                    <li>Return to the WallpaperHub password reset page</li>
                    <li>Enter the 6-digit verification code above</li>
                    <li>Create your new secure password</li>
                    <li>You'll be automatically logged in</li>
                </ol>
            </div>
            
            <div class="warning">
                <h4>⚠️ Important Security Information</h4>
                <p>If you didn't request this password reset, please ignore this email and your password will remain unchanged. For security, this code will expire automatically.</p>
            </div>
            
            <p>If you're having trouble with the password reset process, you can contact our support team for assistance.</p>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p><strong>WallpaperHub</strong> - Transform your digital experience</p>
            <p>This is an automated security email. Please do not reply to this message.</p>
            <p><a href="{{ site_url }}/">Return to WallpaperHub</a></p>
        </div>
    </div>
</body>
</html>
