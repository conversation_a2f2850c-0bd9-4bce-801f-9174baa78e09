# Google OAuth Sign-In Fix for WallpaperHub

This document explains the changes made to fix the Google OAuth sign-in functionality in WallpaperHub.

## What Was Fixed

1. **Redirect URI Mismatch Error**: Fixed the error that occurred when trying to sign in with Google.
2. **Account Selection Page**: Modified the OAuth flow to skip the Google account selection page for a smoother experience.

## Changes Made

### 1. Updated Google OAuth Configuration in settings.py

```python
SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'APP': {
            'client_id': '************-s1b5h06rmcselefiahcgkh7a3vrm4nii.apps.googleusercontent.com',
            'secret': '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',
            'key': ''
        },
        'SCOPE': [
            'profile',
            'email',
        ],
        'AUTH_PARAMS': {
            'access_type': 'online',
            'prompt': 'select_account consent',  # Added this parameter
        }
    }
}

# Added these settings
SOCIALACCOUNT_AUTO_SIGNUP = True
SOCIALACCOUNT_LOGIN_ON_GET = True
SOCIALACCOUNT_EMAIL_REQUIRED = True
SOCIALACCOUNT_EMAIL_VERIFICATION = 'none'
SOCIALACCOUNT_ADAPTER = 'accounts.adapters.CustomSocialAccountAdapter'
```

### 2. Created a Custom Social Account Adapter

Created a new file `accounts/adapters.py` with a custom adapter that:
- Connects social accounts to existing users with the same email
- Saves user data to MongoDB
- Handles profile pictures from Google
- Provides better error handling and user feedback

### 3. Updated Login and Signup Templates

Modified the Google sign-in buttons to use direct URLs instead of template tags:
- Changed from `{% provider_login_url 'google' process='login' %}` to `/accounts/google/login/?process=login`
- Changed from `{% provider_login_url 'google' process='signup' %}` to `/accounts/google/login/?process=signup`

### 4. Updated Login and Signup Views

Enhanced the views to:
- Check if users are already authenticated
- Handle next URL parameters
- Provide context for social login URLs

## How to Test

1. Start your Django development server:
   ```
   python manage.py runserver
   ```

2. Go to the login page:
   ```
   http://127.0.0.1:8000/login/
   ```

3. Click "Sign in with Google" and verify that:
   - You're redirected to Google's authentication page
   - After authentication, you're redirected back to WallpaperHub
   - You don't see the "redirect_uri_mismatch" error

## Troubleshooting

If you still encounter issues:

1. Make sure your Google OAuth credentials in the Google Cloud Console have the correct redirect URI:
   ```
   http://127.0.0.1:8000/accounts/google/login/callback/
   ```

2. Check that your site domain is correctly set in the Django admin:
   ```
   python manage.py update_site_domain 127.0.0.1:8000
   ```

3. Clear your browser cookies and cache before testing again.

4. Check the Django debug logs for any errors related to OAuth authentication.
