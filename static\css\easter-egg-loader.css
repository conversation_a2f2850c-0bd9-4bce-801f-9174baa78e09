/* Easter Egg Animation Loader for WallpaperHub */

.loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
}

.loader-container.hidden {
  opacity: 0;
  visibility: hidden;
}

.easter-animation {
  position: relative;
}

.egg {
  position: relative;
  width: 150px;
  height: 200px;
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  background: linear-gradient(
    295deg,
    #ff595e 15%,
    #8ac926 15%,
    #8ac926 30%,
    #ff595e 30%,
    #ff595e 49%,
    #8ac926 49%,
    #8ac926 70%,
    #ff595e 70%
  );
  overflow: hidden;
  animation: bounce 1s linear infinite;
}

.egg:before,
.egg:after {
  position: absolute;
  content: "";
}

.egg:before {
  width: 200px;
  height: 100px;
  background-color: #1982c4;
  top: 160px;
  box-shadow: 0 -50px #ffca3a, 0 -65px #8ac926, 0 -90px #6a4c93, 0 -95px #ffca3a;
}

.egg:after {
  border-radius: 50%;
  width: 15px;
  height: 15px;
  background-color: #ffca3a;
  left: 90px;
  top: 75px;
  box-shadow: -40px 0 #ffca3a, 40px 0 #ffca3a, -80px 0 #ffca3a, -120px 0 #ffca3a;
}

.eyes {
  position: absolute;
  background-color: #333;
  border-radius: 50%;
  width: 20px;
  height: 30px;
  left: 90px;
  top: 120px;
  box-shadow: 20px 0 #333;
}

.eyes:before {
  position: absolute;
  content: "";
  background-color: #fff;
  width: 5px;
  height: 12px;
  border-radius: 50%;
  left: 12px;
  top: 10px;
  box-shadow: 20px 0 #fff;
}

.eyes:after {
  content: "";
  position: absolute;
  width: 200px;
  height: 30px;
  background-color: #1982c4;
  top: -130px;
  left: -100px;
}

@keyframes bounce {
  0% {
    transform: scale(1, 1) translateY(0);
  }
  10% {
    transform: scale(1.1, 0.9) translateY(0);
  }
  30% {
    transform: scale(0.9, 1.1) translateY(-70px) translateX(30px);
  }
  50% {
    transform: scale(1.05, 0.95) translateY(0) translateX(30px) rotate(-10deg);
  }
  58% {
    transform: scale(1, 1) translateY(-7px) translateX(0);
  }
  65% {
    transform: scale(1, 1) translateY(0);
  }
  100% {
    transform: scale(1, 1) translateY(0);
  }
}

.shadow {
  position: absolute;
  z-index: -1;
  background-color: rgba(0, 0, 0, 0.2);
  width: 100px;
  height: 20px;
  border-radius: 50%;
  left: 20px;
  top: 190px;
  animation: move 1s linear infinite;
}

@keyframes move {
  0% {
    transform: translateX(0);
  }
  10% {
    transform: translateX(0) scale(0.8);
  }
  30% {
    transform: translateX(0) scale(0.8);
  }
  50% {
    transform: translateX(30px) scale(0.7);
  }
  58% {
    transform: translateX(20px);
  }
  65% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(0);
  }
}

.clouds {
  position: absolute;
  width: 500px;
}

.cloud1,
.cloud2,
.cloud3 {
  position: absolute;
  background-color: white;
  width: 100px;
  height: 25px;
  border-radius: 100px;
}

.cloud1:before,
.cloud2:before,
.cloud3:before {
  position: absolute;
  content: "";
  width: 40px;
  height: 40px;
  top: -20px;
  left: 20px;
  background-color: white;
  border-radius: 50%;
}

.cloud1 {
  top: -150px;
  z-index: -1;
  animation: clouds 2s linear infinite;
}

.cloud2 {
  top: -70px;
  opacity: 0.5;
  transform: scale(0.7);
  animation: clouds 1.8s linear infinite;
}

.cloud3 {
  top: -100px;
  opacity: 0.5;
  transform: scale(0.9);
  animation: clouds 1.5s linear infinite;
  z-index: -2;
}

@keyframes clouds {
  from {
    left: 500px;
  }
  to {
    left: -500px;
  }
}
