<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to WallpaperHub Newsletter</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background-color: white;
            border-radius: 12px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .welcome-message {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .welcome-message h2 {
            font-size: 24px;
            color: #333;
            margin-bottom: 15px;
        }
        
        .welcome-message p {
            font-size: 16px;
            color: #666;
            line-height: 1.6;
        }
        
        .features {
            margin: 40px 0;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 18px;
            color: white;
        }
        
        .feature-text h3 {
            font-size: 16px;
            color: #333;
            margin-bottom: 5px;
        }
        
        .feature-text p {
            font-size: 14px;
            color: #666;
        }
        
        .categories {
            margin: 40px 0;
        }
        
        .categories h3 {
            text-align: center;
            font-size: 20px;
            color: #333;
            margin-bottom: 25px;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .category-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            text-decoration: none;
            transition: transform 0.3s ease;
        }
        
        .category-item:hover {
            transform: translateY(-2px);
            text-decoration: none;
            color: white;
        }
        
        .cta-section {
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
        }
        
        .cta-section h3 {
            font-size: 22px;
            margin-bottom: 15px;
        }
        
        .cta-section p {
            font-size: 16px;
            margin-bottom: 25px;
            opacity: 0.9;
        }
        
        .button {
            display: inline-block;
            background-color: white;
            color: #667eea;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            background-color: #f8f9fa;
            text-decoration: none;
            color: #667eea;
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .footer p {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .social-links {
            margin: 20px 0;
        }
        
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
        }
        
        .unsubscribe {
            font-size: 12px;
            color: #999;
            margin-top: 20px;
        }
        
        .unsubscribe a {
            color: #999;
            text-decoration: underline;
        }
        
        /* Mobile responsiveness */
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .header, .content, .footer {
                padding: 20px;
            }
            
            .category-grid {
                grid-template-columns: 1fr;
            }
            
            .feature {
                flex-direction: column;
                text-align: center;
            }
            
            .feature-icon {
                margin-right: 0;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">WH</div>
            <h1>Welcome to WallpaperHub!</h1>
            <p>Your ultimate destination for stunning wallpapers</p>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="welcome-message">
                <h2>Thank you for subscribing! 🎨</h2>
                <p>We're excited to have you join our community of wallpaper enthusiasts. Get ready to discover amazing wallpapers, exclusive content, and the latest updates from WallpaperHub.</p>
            </div>
            
            <!-- Features -->
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🖼️</div>
                    <div class="feature-text">
                        <h3>Thousands of Wallpapers</h3>
                        <p>Explore our vast collection of high-quality wallpapers in every category imaginable.</p>
                    </div>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">💾</div>
                    <div class="feature-text">
                        <h3>Save & Organize</h3>
                        <p>Create collections, save your favorites, and organize wallpapers your way.</p>
                    </div>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">📱</div>
                    <div class="feature-text">
                        <h3>Multiple Resolutions</h3>
                        <p>Download wallpapers in various resolutions perfect for any device.</p>
                    </div>
                </div>
            </div>
            
            <!-- Categories -->
            <div class="categories">
                <h3>Explore Popular Categories</h3>
                <div class="category-grid">
                    {% for category in categories %}
                    <a href="{{ site_url }}{{ category.url }}" class="category-item">
                        {{ category.name }}
                    </a>
                    {% endfor %}
                </div>
            </div>
            
            <!-- CTA Section -->
            <div class="cta-section">
                <h3>Ready to Get Started?</h3>
                <p>Dive into our collection and find the perfect wallpaper for your device!</p>
                <a href="{{ site_url }}/userHome/" class="button">Explore Wallpapers</a>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p><strong>WallpaperHub</strong> - Transform your digital experience</p>
            
            <div class="social-links">
                <a href="{{ site_url }}/">Home</a>
                <a href="{{ site_url }}/aboutUs.html">About Us</a>
                <a href="{{ site_url }}/privacyPolicy.html">Privacy Policy</a>
                <a href="{{ site_url }}/termsOfService.html">Terms of Service</a>
            </div>
            
            <p>© {{ current_year }} WallpaperHub. All rights reserved.</p>
            
            <div class="unsubscribe">
                <p>You're receiving this email because you subscribed to our newsletter.</p>
                <p><a href="{{ unsubscribe_url }}">Unsubscribe</a> | <a href="{{ site_url }}/">Visit Website</a></p>
            </div>
        </div>
    </div>
</body>
</html>
