/* Back to Top Button Styles */
.back-to-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 30px;
  bottom: 30px;
  z-index: 99999;
  width: 50px;
  height: 50px;
  background: var(--primary, #65558F);
  color: #fff;
  border-radius: 50%;
  transition: all 0.4s;
  text-decoration: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 2px solid white;
  transform: translateY(20px);
  pointer-events: none;
}

/* Dark theme styles */
[data-theme="dark"] .back-to-top {
  background: var(--primary, #65558F);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

.back-to-top i {
  font-size: 28px;
  line-height: 0;
}

.back-to-top:hover {
  background: var(--primary-dark, #534679);
  color: #fff;
  transform: translateY(-5px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

/* Dark theme hover state */
[data-theme="dark"] .back-to-top:hover {
  background: var(--primary-dark, #534679);
  color: white;
  transform: translateY(-5px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.6);
  border-color: white;
}

.back-to-top.active {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
  display: flex;
  cursor: pointer;
  pointer-events: auto;
  animation: fadeIn 0.3s;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .back-to-top {
    right: 20px;
    bottom: 20px;
    width: 55px;
    height: 55px;
    border: 3px solid white;
  }

  .back-to-top i {
    font-size: 32px;
  }

  /* Dark theme mobile styles */
  [data-theme="dark"] .back-to-top {
    border: 3px solid white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.6);
  }
}
