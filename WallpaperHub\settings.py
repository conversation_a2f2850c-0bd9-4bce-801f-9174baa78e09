"""
Django settings for WallpaperHub project.

Generated by 'django-admin startproject' using Django 5.1.7.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-g7ac_^t6@4ojbi3(i2h$8bu%l-eeia-slpkco-)gzqiaf+s3s2'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'accounts.apps.AccountsConfig',
    'django.contrib.sites',
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.google',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'allauth.account.middleware.AccountMiddleware',
]

ROOT_URLCONF = 'WallpaperHub.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'Template'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'WallpaperHub.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/
import os
STATIC_URL = 'static/'

STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
    os.path.join(BASE_DIR, 'Template'),
]
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Authentication settings
LOGIN_REDIRECT_URL = '/userHome/'
LOGOUT_REDIRECT_URL = '/landingPage/'

# Django AllAuth settings
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
]

SITE_ID = 1

# Google OAuth2 settings
# Replace these with your own Google OAuth API credentials
SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'APP': {
            'client_id': '************-s1b5h06rmcselefiahcgkh7a3vrm4nii.apps.googleusercontent.com',  # Replace with your Google Client ID
            'secret': '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',  # Replace with your Google Client Secret
            'key': ''
        },
        'SCOPE': [
            'profile',
            'email',
            'openid',
        ],
        'AUTH_PARAMS': {
            'access_type': 'online',
            'prompt': 'select_account consent',
        },
        # Disable JWT validation for development
        'OAUTH_PKCE_ENABLED': True,
        'JWT_VERIFY': False,
        'JWT_AUTH_HEADER_PREFIX': 'Bearer',
    }
}

# AllAuth account settings
ACCOUNT_LOGIN_METHODS = {'email'}
ACCOUNT_SIGNUP_FIELDS = ['email*', 'password1*', 'password2*']
ACCOUNT_EMAIL_VERIFICATION = 'none'

# Social Account Settings
SOCIALACCOUNT_AUTO_SIGNUP = True  # Auto-signup for social accounts
SOCIALACCOUNT_LOGIN_ON_GET = True  # Skip the intermediate login page
SOCIALACCOUNT_EMAIL_REQUIRED = True  # Require email from social accounts
SOCIALACCOUNT_EMAIL_VERIFICATION = 'none'  # Don't verify emails from social accounts
SOCIALACCOUNT_ADAPTER = 'accounts.adapters.CustomSocialAccountAdapter'  # Custom adapter for better handling

# Email Settings
ACCOUNT_EMAIL_SUBJECT_PREFIX = '[WallpaperHub] '  # Prefix for email subjects
ACCOUNT_EMAIL_CONFIRMATION_EXPIRE_DAYS = 3  # How long email confirmations are valid
ACCOUNT_EMAIL_CONFIRMATION_AUTHENTICATED_REDIRECT_URL = None  # Where to redirect after confirmation if logged in
ACCOUNT_EMAIL_CONFIRMATION_ANONYMOUS_REDIRECT_URL = '/login/'  # Where to redirect after confirmation if not logged in
ACCOUNT_EMAIL_CONFIRMATION_COOLDOWN = 180  # Cooldown period for email confirmations (3 minutes)

# Email Configuration
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'  # For development - prints to console
# For production, use SMTP:
# EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
# EMAIL_HOST = 'smtp.gmail.com'
# EMAIL_PORT = 587
# EMAIL_USE_TLS = True
# EMAIL_HOST_USER = '<EMAIL>'
# EMAIL_HOST_PASSWORD = 'your-app-password'
DEFAULT_FROM_EMAIL = '<EMAIL>'
SITE_URL = 'http://127.0.0.1:8000'  # Base URL for the site

# SMS Configuration
SMS_PROVIDER = 'console'  # Options: 'console', 'twilio', 'aws_sns'

# Twilio Configuration (uncomment and configure for production)
# TWILIO_ACCOUNT_SID = 'your-twilio-account-sid'
# TWILIO_AUTH_TOKEN = 'your-twilio-auth-token'
# TWILIO_FROM_NUMBER = '+**********'  # Your Twilio phone number

# AWS SNS Configuration (uncomment and configure for production)
# AWS_ACCESS_KEY_ID = 'your-aws-access-key'
# AWS_SECRET_ACCESS_KEY = 'your-aws-secret-key'
# AWS_REGION = 'us-east-1'

# Disable email notifications for login
ACCOUNT_EMAIL_CONFIRMATION_HMAC = True
ACCOUNT_EMAIL_CONFIRMATION_AUTHENTICATED_REDIRECT_URL = '/userHome/'
ACCOUNT_AUTHENTICATION_METHOD = 'email'  # Use email for authentication
ACCOUNT_EMAIL_REQUIRED = True  # Email is required
ACCOUNT_UNIQUE_EMAIL = True  # Email must be unique
ACCOUNT_USERNAME_REQUIRED = False  # Username is not required
ACCOUNT_USER_MODEL_USERNAME_FIELD = None  # Don't use username field
ACCOUNT_LOGOUT_ON_PASSWORD_CHANGE = False  # Don't log out on password change
ACCOUNT_LOGOUT_REDIRECT_URL = '/landingPage/'  # Where to redirect after logout
ACCOUNT_PRESERVE_USERNAME_CASING = False  # Don't preserve username casing
ACCOUNT_SESSION_REMEMBER = True  # Remember me is on by default
ACCOUNT_SIGNUP_PASSWORD_ENTER_TWICE = True  # Enter password twice on signup
ACCOUNT_SIGNUP_EMAIL_ENTER_TWICE = False  # Don't enter email twice on signup
ACCOUNT_LOGIN_ON_EMAIL_CONFIRMATION = True  # Log in after email confirmation
ACCOUNT_LOGIN_ON_PASSWORD_RESET = True  # Log in after password reset
ACCOUNT_SEND_LOGIN_EMAIL = True  # Send email on login
