/* Explore More But<PERSON> Styles */
.explore-more-btn {
    position: relative;
    padding: 10px 20px;
    background-color: #65558f;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer !important;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    outline: none;
}

.explore-more-btn:hover {
    background-color: #534679;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.explore-more-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.explore-more-btn:disabled {
    background-color: #a9a9a9;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
    vertical-align: middle;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Staggered Animation for Wallpaper Items */
.wallpaper-item {
    transition: opacity 0.5s ease, transform 0.5s ease;
}

/* Loading Placeholder Animation */
.loading-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #f0f0f0 0%, #f8f8f8 50%, #f0f0f0 100%);
    background-size: 200% 100%;
    animation: loadingAnimation 1.5s infinite;
    z-index: 1;
    transition: opacity 0.3s ease;
}

@keyframes loadingAnimation {
    0% { background-position: 0% 0; }
    100% { background-position: -200% 0; }
}

/* Explore More Container */
.explore-more-container {
    display: flex;
    justify-content: center;
    margin: 30px 0;
    padding: 10px;
}

#load-more-btn, .modern-load-more-btn, .modern-load-more-btn * {
  cursor: pointer !important;
  pointer-events: auto !important;
  z-index: 9999 !important;
}
