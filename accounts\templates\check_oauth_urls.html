{% extends "base.html" %}
{% load socialaccount %}

{% block content %}
<div class="container mt-5">
    <h1>Google OAuth Configuration</h1>
    <div class="card mt-4">
        <div class="card-header">
            <h3>Current OAuth URLs</h3>
        </div>
        <div class="card-body">
            <h4>Callback URLs</h4>
            <p>These are the URLs that should be registered in your Google OAuth credentials:</p>
            <div class="alert alert-info">
                <strong>Callback URL:</strong> {{ request.scheme }}://{{ request.get_host }}/accounts/google/login/callback/
            </div>

            <h4>Current Site Configuration</h4>
            <p>Make sure your site domain is correctly configured in the Django admin:</p>
            <div class="alert alert-warning">
                <strong>Current Site Domain:</strong> {{ current_site.domain }}
            </div>

            <h4>Instructions</h4>
            <ol>
                <li>Go to the <a href="https://console.cloud.google.com/apis/credentials" target="_blank">Google Cloud Console</a></li>
                <li>Select your project</li>
                <li>Edit your OAuth 2.0 Client ID</li>
                <li>Add the above callback URLs to the "Authorized redirect URIs" section</li>
                <li>Save your changes</li>
            </ol>
        </div>
    </div>
</div>
{% endblock %}
