# Generated by Django 5.2 on 2025-04-17 05:40

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, max_length=100)),
                ('color', models.CharField(blank=True, max_length=20)),
                ('cover_image_url', models.URLField(blank=True, max_length=500)),
                ('display_order', models.PositiveIntegerField(default=0)),
                ('wallpaper_count', models.PositiveIntegerField(default=0)),
                ('view_count', models.PositiveIntegerField(default=0)),
                ('is_featured', models.BooleanField(default=False)),
                ('featured_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('mongo_id', models.CharField(blank=True, max_length=24)),
            ],
            options={
                'verbose_name': 'Category',
                'verbose_name_plural': 'Categories',
                'ordering': ['display_order', 'name'],
                'indexes': [models.Index(fields=['slug'], name='accounts_ca_slug_5ab157_idx'), models.Index(fields=['is_featured'], name='accounts_ca_is_feat_6011f9_idx'), models.Index(fields=['display_order'], name='accounts_ca_display_fa35c9_idx')],
            },
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('usage_count', models.PositiveIntegerField(default=0)),
                ('search_count', models.PositiveIntegerField(default=0)),
                ('mongo_id', models.CharField(blank=True, max_length=24)),
            ],
            options={
                'verbose_name': 'Tag',
                'verbose_name_plural': 'Tags',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['slug'], name='accounts_ta_slug_b84ce0_idx'), models.Index(fields=['usage_count'], name='accounts_ta_usage_c_61d4ca_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserPreferences',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('preferred_categories', models.JSONField(blank=True, default=list)),
                ('preferred_tags', models.JSONField(blank=True, default=list)),
                ('preferred_aspect_ratio', models.CharField(choices=[('any', 'Any'), ('portrait', 'Portrait'), ('landscape', 'Landscape'), ('square', 'Square'), ('ultrawide', 'Ultrawide')], default='any', max_length=20)),
                ('preferred_colors', models.JSONField(blank=True, default=list)),
                ('show_nsfw_content', models.BooleanField(default=False)),
                ('hide_viewed_wallpapers', models.BooleanField(default=False)),
                ('hide_downloaded_wallpapers', models.BooleanField(default=False)),
                ('show_trending', models.BooleanField(default=True)),
                ('show_featured', models.BooleanField(default=True)),
                ('show_new', models.BooleanField(default=True)),
                ('show_recommendations', models.BooleanField(default=True)),
                ('default_download_quality', models.CharField(choices=[('original', 'Original'), ('high', 'High'), ('medium', 'Medium'), ('low', 'Low')], default='high', max_length=20)),
                ('auto_resize_to_screen', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('mongo_id', models.CharField(blank=True, max_length=24)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Preferences',
                'verbose_name_plural': 'User Preferences',
            },
        ),
        migrations.CreateModel(
            name='UserSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('theme', models.CharField(choices=[('light', 'Light'), ('dark', 'Dark'), ('system', 'System Default')], default='system', max_length=10)),
                ('accent_color', models.CharField(default='#65558F', max_length=7)),
                ('layout_density', models.CharField(choices=[('compact', 'Compact'), ('medium', 'Medium'), ('comfortable', 'Comfortable')], default='medium', max_length=15)),
                ('show_email', models.BooleanField(default=True)),
                ('show_activity', models.BooleanField(default=True)),
                ('allow_data_collection', models.BooleanField(default=True)),
                ('email_notifications', models.BooleanField(default=True)),
                ('site_notifications', models.BooleanField(default=True)),
                ('connected_google', models.BooleanField(default=False)),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('last_active', models.DateTimeField(default=django.utils.timezone.now)),
                ('mongo_id', models.CharField(blank=True, max_length=24)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='settings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Settings',
                'verbose_name_plural': 'User Settings',
            },
        ),
        migrations.CreateModel(
            name='UserStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_views', models.PositiveIntegerField(default=0)),
                ('total_downloads', models.PositiveIntegerField(default=0)),
                ('total_likes', models.PositiveIntegerField(default=0)),
                ('total_shares', models.PositiveIntegerField(default=0)),
                ('total_saves', models.PositiveIntegerField(default=0)),
                ('total_uploads', models.PositiveIntegerField(default=0)),
                ('total_collections', models.PositiveIntegerField(default=0)),
                ('total_comments', models.PositiveIntegerField(default=0)),
                ('total_time_spent', models.PositiveIntegerField(default=0)),
                ('category_interactions', models.JSONField(blank=True, default=dict)),
                ('tag_interactions', models.JSONField(blank=True, default=dict)),
                ('daily_activity', models.JSONField(blank=True, default=dict)),
                ('weekly_activity', models.JSONField(blank=True, default=dict)),
                ('monthly_activity', models.JSONField(blank=True, default=dict)),
                ('device_usage', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_interaction_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('mongo_id', models.CharField(blank=True, max_length=24)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='statistics', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Statistics',
                'verbose_name_plural': 'User Statistics',
            },
        ),
        migrations.CreateModel(
            name='Wallpaper',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('image_url', models.URLField(blank=True, max_length=500)),
                ('image_path', models.CharField(blank=True, max_length=500)),
                ('small_url', models.URLField(blank=True, max_length=500)),
                ('regular_url', models.URLField(blank=True, max_length=500)),
                ('full_url', models.URLField(blank=True, max_length=500)),
                ('tags_string', models.CharField(blank=True, max_length=500)),
                ('category', models.CharField(blank=True, max_length=100)),
                ('is_featured', models.BooleanField(default=False)),
                ('views', models.PositiveIntegerField(default=0)),
                ('likes', models.PositiveIntegerField(default=0)),
                ('downloads', models.PositiveIntegerField(default=0)),
                ('shares', models.PositiveIntegerField(default=0)),
                ('daily_views', models.JSONField(blank=True, default=dict)),
                ('weekly_views', models.JSONField(blank=True, default=dict)),
                ('monthly_views', models.JSONField(blank=True, default=dict)),
                ('unsplash_id', models.CharField(blank=True, max_length=100)),
                ('custom_upload', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_viewed_at', models.DateTimeField(blank=True, null=True)),
                ('last_downloaded_at', models.DateTimeField(blank=True, null=True)),
                ('last_shared_at', models.DateTimeField(blank=True, null=True)),
                ('featured_at', models.DateTimeField(blank=True, null=True)),
                ('mongo_id', models.CharField(blank=True, max_length=24)),
                ('category_obj', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='wallpapers', to='accounts.category')),
                ('downloaded_by_users', models.ManyToManyField(blank=True, related_name='downloaded_wallpapers', to=settings.AUTH_USER_MODEL)),
                ('liked_by_users', models.ManyToManyField(blank=True, related_name='liked_wallpapers', to=settings.AUTH_USER_MODEL)),
                ('saved_by_users', models.ManyToManyField(blank=True, related_name='saved_wallpapers', to=settings.AUTH_USER_MODEL)),
                ('tags', models.ManyToManyField(blank=True, related_name='wallpapers', to='accounts.tag')),
                ('uploaded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_wallpapers', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Wallpaper',
                'verbose_name_plural': 'Wallpapers',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Comment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content', models.TextField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('edited', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('pending', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('flagged', 'Flagged for Review')], default='approved', max_length=20)),
                ('moderated_at', models.DateTimeField(blank=True, null=True)),
                ('moderation_reason', models.TextField(blank=True)),
                ('likes', models.PositiveIntegerField(default=0)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('mongo_id', models.CharField(blank=True, max_length=24)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to=settings.AUTH_USER_MODEL)),
                ('liked_by', models.ManyToManyField(blank=True, related_name='liked_comments', to=settings.AUTH_USER_MODEL)),
                ('moderated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='moderated_comments', to=settings.AUTH_USER_MODEL)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='accounts.comment')),
                ('wallpaper', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='accounts.wallpaper')),
            ],
            options={
                'verbose_name': 'Comment',
                'verbose_name_plural': 'Comments',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['wallpaper'], name='accounts_co_wallpap_c78dae_idx'), models.Index(fields=['author'], name='accounts_co_author__1ce299_idx'), models.Index(fields=['parent'], name='accounts_co_parent__ca5e98_idx'), models.Index(fields=['status'], name='accounts_co_status_b2cd70_idx'), models.Index(fields=['created_at'], name='accounts_co_created_da492a_idx')],
            },
        ),
        migrations.CreateModel(
            name='Collection',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('custom_cover_url', models.URLField(blank=True, max_length=500)),
                ('privacy', models.CharField(choices=[('public', 'Public'), ('unlisted', 'Unlisted'), ('private', 'Private')], default='private', max_length=10)),
                ('featured', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_modified_at', models.DateTimeField(auto_now=True)),
                ('view_count', models.PositiveIntegerField(default=0)),
                ('sort_order', models.CharField(choices=[('date_added', 'Date Added'), ('date_added_reverse', 'Date Added (Reverse)'), ('title', 'Title'), ('popularity', 'Popularity'), ('custom', 'Custom Order')], default='date_added', max_length=20)),
                ('mongo_id', models.CharField(blank=True, max_length=24)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='collections', to=settings.AUTH_USER_MODEL)),
                ('cover_image', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='cover_for_collections', to='accounts.wallpaper')),
                ('wallpapers', models.ManyToManyField(blank=True, related_name='collections', to='accounts.wallpaper')),
            ],
            options={
                'verbose_name': 'Collection',
                'verbose_name_plural': 'Collections',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['owner'], name='accounts_co_owner_i_df14d8_idx'), models.Index(fields=['privacy'], name='accounts_co_privacy_46dde8_idx'), models.Index(fields=['featured'], name='accounts_co_feature_69bdc9_idx')],
                'unique_together': {('owner', 'name')},
            },
        ),
        migrations.CreateModel(
            name='WallpaperInteraction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('interaction_type', models.CharField(choices=[('view', 'View'), ('like', 'Like'), ('unlike', 'Unlike'), ('download', 'Download'), ('save', 'Save'), ('unsave', 'Unsave'), ('share', 'Share')], max_length=20)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('device_type', models.CharField(blank=True, max_length=20)),
                ('session_id', models.CharField(blank=True, max_length=40)),
                ('share_platform', models.CharField(blank=True, max_length=50)),
                ('referrer', models.URLField(blank=True, max_length=500)),
                ('duration', models.PositiveIntegerField(default=0)),
                ('mongo_id', models.CharField(blank=True, max_length=24)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wallpaper_interactions', to=settings.AUTH_USER_MODEL)),
                ('wallpaper', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_interactions', to='accounts.wallpaper')),
            ],
            options={
                'verbose_name': 'Wallpaper Interaction',
                'verbose_name_plural': 'Wallpaper Interactions',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', 'wallpaper'], name='accounts_wa_user_id_ee8939_idx'), models.Index(fields=['interaction_type'], name='accounts_wa_interac_c3f658_idx'), models.Index(fields=['timestamp'], name='accounts_wa_timesta_593d0e_idx')],
            },
        ),
    ]
