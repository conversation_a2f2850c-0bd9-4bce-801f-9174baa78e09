<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - WallpaperHub</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary: #667eea;
            --primary-rgb: 102, 126, 234;
            --secondary: #764ba2;
            --dark-bg: #0E121B;
            --text-color: #333;
            --border-color: #e9ecef;
            --success: #28a745;
            --danger: #dc3545;
            --warning: #ffc107;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, var(--dark-bg) 0%, var(--primary) 100%);
            min-height: 100vh;
            color: var(--text-color);
        }
        
        .forgot-password-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .forgot-password-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            position: relative;
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--dark-bg) 0%, var(--primary) 100%);
            color: white;
            padding: 40px 30px 30px;
            text-align: center;
            position: relative;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 20px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: bold;
            color: var(--dark-bg);
        }
        
        .card-header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .card-header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .progress-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0 20px;
            position: relative;
        }
        
        .progress-step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }
        
        .progress-step.active {
            background: white;
            color: var(--dark-bg);
            transform: scale(1.1);
        }
        
        .progress-step.completed {
            background: var(--success);
            color: white;
        }
        
        .progress-line {
            height: 2px;
            background: rgba(255, 255, 255, 0.2);
            flex: 1;
            margin: 0 10px;
            position: relative;
            overflow: hidden;
        }
        
        .progress-line.completed::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            background: var(--success);
            animation: progressFill 0.5s ease;
        }
        
        @keyframes progressFill {
            from { width: 0; }
            to { width: 100%; }
        }
        
        .card-body {
            padding: 40px 30px;
        }
        
        .step-content {
            display: none;
        }
        
        .step-content.active {
            display: block;
            animation: fadeInUp 0.5s ease;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-control {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(var(--primary-rgb), 0.3);
        }
        
        .btn-primary:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        
        .contact-type-toggle {
            display: flex;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 20px;
        }
        
        .contact-type-btn {
            flex: 1;
            padding: 12px;
            border: none;
            background: transparent;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .contact-type-btn.active {
            background: white;
            color: var(--primary);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .otp-inputs {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 30px 0;
        }
        
        .otp-input {
            width: 50px;
            height: 60px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        
        .otp-input:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
            transform: scale(1.05);
        }
        
        .password-strength {
            margin-top: 15px;
        }
        
        .strength-bar {
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
        
        .strength-weak { background: var(--danger); width: 25%; }
        .strength-fair { background: var(--warning); width: 50%; }
        .strength-good { background: var(--primary); width: 75%; }
        .strength-strong { background: var(--success); width: 100%; }
        
        .alert {
            border-radius: 12px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 20px;
            animation: slideDown 0.3s ease;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .loading-spinner {
            display: none;
            margin-right: 10px;
        }
        
        .btn-loading .loading-spinner {
            display: inline-block;
        }
        
        .btn-loading .btn-text {
            opacity: 0.7;
        }
        
        .resend-timer {
            color: var(--primary);
            font-weight: 600;
        }
        
        .back-to-login {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }
        
        .back-to-login a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .back-to-login a:hover {
            color: var(--secondary);
            text-decoration: underline;
        }
        
        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .forgot-password-container {
                padding: 10px;
            }
            
            .forgot-password-card {
                border-radius: 15px;
            }
            
            .card-header {
                padding: 30px 20px 20px;
            }
            
            .card-body {
                padding: 30px 20px;
            }
            
            .logo {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }
            
            .card-header h1 {
                font-size: 24px;
            }
            
            .otp-inputs {
                gap: 8px;
            }
            
            .otp-input {
                width: 45px;
                height: 55px;
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="forgot-password-container">
        <div class="forgot-password-card">
            <!-- Header -->
            <div class="card-header">
                <div class="logo">WH</div>
                <h1>Reset Password</h1>
                <p>Secure password recovery for your account</p>
                
                <!-- Progress Indicator -->
                <div class="progress-indicator">
                    <div class="progress-step active" id="step1-indicator">1</div>
                    <div class="progress-line" id="line1"></div>
                    <div class="progress-step" id="step2-indicator">2</div>
                    <div class="progress-line" id="line2"></div>
                    <div class="progress-step" id="step3-indicator">3</div>
                </div>
            </div>
            
            <!-- Body -->
            <div class="card-body">
                <!-- Alert Messages -->
                <div id="alert-container"></div>
                
                <!-- Step 1: Contact Input -->
                <div class="step-content active" id="step1">
                    <h3 class="mb-4 text-center">Enter Your Contact Information</h3>
                    
                    <div class="contact-type-toggle">
                        <button type="button" class="contact-type-btn active" data-type="email">
                            <i class="bi bi-envelope me-2"></i>Email
                        </button>
                        <button type="button" class="contact-type-btn" data-type="phone">
                            <i class="bi bi-phone me-2"></i>Phone
                        </button>
                    </div>
                    
                    <form id="step1-form">
                        {% csrf_token %}
                        <div class="form-floating">
                            <input type="email" class="form-control" id="contact-input" placeholder="Enter your email address" required>
                            <label for="contact-input" id="contact-label">Email Address</label>
                        </div>

                        <div class="form-text mt-2 mb-3">
                            <small class="text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                <span id="contact-help-text">We'll send a verification code to your email address</span>
                            </small>
                        </div>

                        <button type="submit" class="btn btn-primary w-100" id="step1-btn">
                            <span class="loading-spinner spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <span class="btn-text">Send Verification Code</span>
                        </button>
                    </form>
                </div>

                <!-- Step 2: OTP Verification -->
                <div class="step-content" id="step2">
                    <h3 class="mb-3 text-center">Enter Verification Code</h3>
                    <p class="text-center text-muted mb-4">
                        We've sent a 6-digit code to <strong id="masked-contact"></strong>
                    </p>

                    <form id="step2-form">
                        {% csrf_token %}
                        <div class="otp-inputs">
                            <input type="text" class="form-control otp-input" maxlength="1" data-index="0">
                            <input type="text" class="form-control otp-input" maxlength="1" data-index="1">
                            <input type="text" class="form-control otp-input" maxlength="1" data-index="2">
                            <input type="text" class="form-control otp-input" maxlength="1" data-index="3">
                            <input type="text" class="form-control otp-input" maxlength="1" data-index="4">
                            <input type="text" class="form-control otp-input" maxlength="1" data-index="5">
                        </div>

                        <button type="submit" class="btn btn-primary w-100" id="step2-btn">
                            <span class="loading-spinner spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <span class="btn-text">Verify Code</span>
                        </button>
                    </form>

                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-link" id="resend-otp-btn">
                            <span id="resend-text">Resend Code</span>
                            <span id="resend-timer" class="resend-timer" style="display: none;"></span>
                        </button>
                    </div>
                </div>

                <!-- Step 3: Password Reset -->
                <div class="step-content" id="step3">
                    <h3 class="mb-4 text-center">Create New Password</h3>

                    <form id="step3-form">
                        {% csrf_token %}
                        <div class="form-floating">
                            <input type="password" class="form-control" id="new-password" placeholder="Enter new password" required>
                            <label for="new-password">New Password</label>
                        </div>

                        <div class="password-strength">
                            <div class="strength-bar">
                                <div class="strength-fill" id="strength-fill"></div>
                            </div>
                            <div class="strength-text" id="strength-text"></div>
                        </div>

                        <div class="form-floating">
                            <input type="password" class="form-control" id="confirm-password" placeholder="Confirm new password" required>
                            <label for="confirm-password">Confirm Password</label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100" id="step3-btn">
                            <span class="loading-spinner spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <span class="btn-text">Reset Password</span>
                        </button>
                    </form>
                </div>

                <!-- Back to Login -->
                <div class="back-to-login">
                    <a href="/login/">
                        <i class="bi bi-arrow-left me-2"></i>
                        Back to Login
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Global variables
            let currentStep = 1;
            let contactType = 'email';
            let resendTimer = null;
            let resendCountdown = 0;

            // DOM elements
            const step1Form = document.getElementById('step1-form');
            const step2Form = document.getElementById('step2-form');
            const step3Form = document.getElementById('step3-form');
            const contactInput = document.getElementById('contact-input');
            const contactLabel = document.getElementById('contact-label');
            const maskedContact = document.getElementById('masked-contact');
            const otpInputs = document.querySelectorAll('.otp-input');
            const newPasswordInput = document.getElementById('new-password');
            const confirmPasswordInput = document.getElementById('confirm-password');
            const strengthFill = document.getElementById('strength-fill');
            const strengthText = document.getElementById('strength-text');
            const resendBtn = document.getElementById('resend-otp-btn');
            const resendText = document.getElementById('resend-text');
            const resendTimerEl = document.getElementById('resend-timer');

            // Contact type toggle
            document.querySelectorAll('.contact-type-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.contact-type-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    contactType = this.dataset.type;

                    if (contactType === 'email') {
                        contactInput.type = 'email';
                        contactInput.placeholder = 'Enter your email address';
                        contactLabel.textContent = 'Email Address';
                        document.getElementById('contact-help-text').textContent = "We'll send a verification code to your email address";
                    } else {
                        contactInput.type = 'tel';
                        contactInput.placeholder = 'Enter your phone number';
                        contactLabel.textContent = 'Phone Number';
                        document.getElementById('contact-help-text').textContent = "We'll send a verification code to your phone number";
                    }

                    contactInput.value = '';
                    clearAlert();
                });
            });

            // Phone number formatting
            contactInput.addEventListener('input', function() {
                if (contactType === 'phone') {
                    formatPhoneNumber(this);
                }
            });

            // Step 1: Contact input form
            step1Form.addEventListener('submit', async function(e) {
                e.preventDefault();

                const contactValue = contactInput.value.trim();
                if (!contactValue) {
                    showAlert('Please enter your contact information.', 'danger');
                    return;
                }

                setButtonLoading('step1-btn', true);
                clearAlert();

                console.log('=== FORGOT PASSWORD STEP 1 DEBUG ===');
                console.log('Contact Value:', contactValue);
                console.log('Contact Type:', contactType);

                try {
                    const csrfToken = getCsrfToken();
                    console.log('CSRF Token:', csrfToken);

                    const requestData = {
                        contact_value: contactValue,
                        contact_type: contactType
                    };
                    console.log('Request Data:', requestData);

                    const response = await fetch('/forgot-password/step1/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify(requestData)
                    });

                    console.log('Response Status:', response.status);
                    console.log('Response OK:', response.ok);

                    const data = await response.json();
                    console.log('Response Data:', data);

                    if (data.success) {
                        maskedContact.textContent = data.contact_masked;
                        showAlert(data.message, 'success');
                        setTimeout(() => {
                            goToStep(2);
                            startResendTimer();
                        }, 1500);
                    } else {
                        showAlert(data.error, 'danger');
                    }
                } catch (error) {
                    console.error('Step 1 error:', error);
                    console.error('Error details:', error.message);
                    console.error('Error stack:', error.stack);
                    showAlert('Network error. Please try again.', 'danger');
                } finally {
                    setButtonLoading('step1-btn', false);
                }
            });

            // OTP input handling
            otpInputs.forEach((input, index) => {
                input.addEventListener('input', function(e) {
                    const value = e.target.value;

                    // Only allow digits
                    if (!/^\d$/.test(value)) {
                        e.target.value = '';
                        return;
                    }

                    // Move to next input
                    if (value && index < otpInputs.length - 1) {
                        otpInputs[index + 1].focus();
                    }

                    // Auto-submit when all fields are filled
                    if (index === otpInputs.length - 1 && value) {
                        const otp = Array.from(otpInputs).map(input => input.value).join('');
                        if (otp.length === 6) {
                            setTimeout(() => step2Form.dispatchEvent(new Event('submit')), 100);
                        }
                    }
                });

                input.addEventListener('keydown', function(e) {
                    // Handle backspace
                    if (e.key === 'Backspace' && !e.target.value && index > 0) {
                        otpInputs[index - 1].focus();
                        otpInputs[index - 1].value = '';
                    }
                });

                input.addEventListener('paste', function(e) {
                    e.preventDefault();
                    const paste = (e.clipboardData || window.clipboardData).getData('text');
                    const digits = paste.replace(/\D/g, '').slice(0, 6);

                    digits.split('').forEach((digit, i) => {
                        if (otpInputs[i]) {
                            otpInputs[i].value = digit;
                        }
                    });

                    if (digits.length === 6) {
                        setTimeout(() => step2Form.dispatchEvent(new Event('submit')), 100);
                    }
                });
            });

            // Step 2: OTP verification form
            step2Form.addEventListener('submit', async function(e) {
                e.preventDefault();

                const otp = Array.from(otpInputs).map(input => input.value).join('');
                if (otp.length !== 6) {
                    showAlert('Please enter the complete 6-digit code.', 'danger');
                    return;
                }

                setButtonLoading('step2-btn', true);
                clearAlert();

                try {
                    const response = await fetch('/forgot-password/step2/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCsrfToken(),
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            otp_code: otp
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        showAlert(data.message, 'success');
                        setTimeout(() => {
                            goToStep(3);
                        }, 1500);
                    } else {
                        showAlert(data.error, 'danger');
                        if (data.attempts_remaining !== undefined) {
                            showAlert(`${data.attempts_remaining} attempts remaining.`, 'warning');
                        }
                    }
                } catch (error) {
                    console.error('Step 2 error:', error);
                    showAlert('Network error. Please try again.', 'danger');
                } finally {
                    setButtonLoading('step2-btn', false);
                }
            });

            // Resend OTP
            resendBtn.addEventListener('click', async function() {
                if (resendCountdown > 0) return;

                setButtonLoading('resend-otp-btn', true);
                clearAlert();

                try {
                    const response = await fetch('/forgot-password/resend-otp/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCsrfToken(),
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });

                    const data = await response.json();

                    if (data.success) {
                        showAlert(data.message, 'success');
                        startResendTimer();
                        // Clear OTP inputs
                        otpInputs.forEach(input => input.value = '');
                        otpInputs[0].focus();
                    } else {
                        showAlert(data.error, 'danger');
                    }
                } catch (error) {
                    console.error('Resend OTP error:', error);
                    showAlert('Network error. Please try again.', 'danger');
                } finally {
                    setButtonLoading('resend-otp-btn', false);
                }
            });

            // Password strength checking
            newPasswordInput.addEventListener('input', function() {
                checkPasswordStrength(this.value);
                validatePasswordMatch();
            });

            confirmPasswordInput.addEventListener('input', function() {
                validatePasswordMatch();
            });

            // Step 3: Password reset form
            step3Form.addEventListener('submit', async function(e) {
                e.preventDefault();

                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                if (!newPassword || !confirmPassword) {
                    showAlert('Please fill in both password fields.', 'danger');
                    return;
                }

                if (newPassword !== confirmPassword) {
                    showAlert('Passwords do not match.', 'danger');
                    return;
                }

                if (newPassword.length < 8) {
                    showAlert('Password must be at least 8 characters long.', 'danger');
                    return;
                }

                setButtonLoading('step3-btn', true);
                clearAlert();

                try {
                    const response = await fetch('/forgot-password/step3/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCsrfToken(),
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            new_password: newPassword,
                            confirm_password: confirmPassword
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        showAlert(data.message, 'success');
                        setTimeout(() => {
                            window.location.href = data.redirect_url || '/userHome/';
                        }, 2000);
                    } else {
                        showAlert(data.error, 'danger');
                    }
                } catch (error) {
                    console.error('Step 3 error:', error);
                    showAlert('Network error. Please try again.', 'danger');
                } finally {
                    setButtonLoading('step3-btn', false);
                }
            });

            // Helper functions
            function goToStep(step) {
                // Hide all steps
                document.querySelectorAll('.step-content').forEach(el => {
                    el.classList.remove('active');
                });

                // Show target step
                document.getElementById(`step${step}`).classList.add('active');

                // Update progress indicators
                updateProgressIndicators(step);

                currentStep = step;

                // Focus on first input of the step
                setTimeout(() => {
                    if (step === 1) {
                        contactInput.focus();
                    } else if (step === 2) {
                        otpInputs[0].focus();
                    } else if (step === 3) {
                        newPasswordInput.focus();
                    }
                }, 300);
            }

            function updateProgressIndicators(step) {
                // Reset all indicators
                document.querySelectorAll('.progress-step').forEach(el => {
                    el.classList.remove('active', 'completed');
                });
                document.querySelectorAll('.progress-line').forEach(el => {
                    el.classList.remove('completed');
                });

                // Update indicators based on current step
                for (let i = 1; i <= 3; i++) {
                    const indicator = document.getElementById(`step${i}-indicator`);
                    if (i < step) {
                        indicator.classList.add('completed');
                        indicator.innerHTML = '<i class="bi bi-check"></i>';
                    } else if (i === step) {
                        indicator.classList.add('active');
                        indicator.textContent = i;
                    } else {
                        indicator.textContent = i;
                    }
                }

                // Update progress lines
                for (let i = 1; i <= 2; i++) {
                    if (i < step) {
                        document.getElementById(`line${i}`).classList.add('completed');
                    }
                }
            }

            function startResendTimer() {
                resendCountdown = 60; // 60 seconds
                resendBtn.disabled = true;
                resendText.style.display = 'none';
                resendTimerEl.style.display = 'inline';

                resendTimer = setInterval(() => {
                    resendCountdown--;
                    resendTimerEl.textContent = `Resend in ${resendCountdown}s`;

                    if (resendCountdown <= 0) {
                        clearInterval(resendTimer);
                        resendBtn.disabled = false;
                        resendText.style.display = 'inline';
                        resendTimerEl.style.display = 'none';
                    }
                }, 1000);
            }

            function checkPasswordStrength(password) {
                let score = 0;
                let feedback = [];

                if (password.length >= 8) score++;
                else feedback.push('At least 8 characters');

                if (/[a-z]/.test(password)) score++;
                else feedback.push('Lowercase letters');

                if (/[A-Z]/.test(password)) score++;
                else feedback.push('Uppercase letters');

                if (/\d/.test(password)) score++;
                else feedback.push('Numbers');

                if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++;
                else feedback.push('Special characters');

                // Update strength bar
                strengthFill.className = 'strength-fill';
                if (score <= 1) {
                    strengthFill.classList.add('strength-weak');
                    strengthText.textContent = 'Weak password';
                    strengthText.style.color = '#dc3545';
                } else if (score <= 2) {
                    strengthFill.classList.add('strength-fair');
                    strengthText.textContent = 'Fair password';
                    strengthText.style.color = '#ffc107';
                } else if (score <= 3) {
                    strengthFill.classList.add('strength-good');
                    strengthText.textContent = 'Good password';
                    strengthText.style.color = '#667eea';
                } else {
                    strengthFill.classList.add('strength-strong');
                    strengthText.textContent = 'Strong password';
                    strengthText.style.color = '#28a745';
                }

                if (feedback.length > 0 && password.length > 0) {
                    strengthText.textContent += ` (Add: ${feedback.join(', ')})`;
                }
            }

            function validatePasswordMatch() {
                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                if (confirmPassword && newPassword !== confirmPassword) {
                    confirmPasswordInput.setCustomValidity('Passwords do not match');
                    confirmPasswordInput.classList.add('is-invalid');
                } else {
                    confirmPasswordInput.setCustomValidity('');
                    confirmPasswordInput.classList.remove('is-invalid');
                }
            }

            function showAlert(message, type) {
                const alertContainer = document.getElementById('alert-container');
                const alertEl = document.createElement('div');
                alertEl.className = `alert alert-${type}`;
                alertEl.innerHTML = `
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                `;

                alertContainer.innerHTML = '';
                alertContainer.appendChild(alertEl);

                // Auto-hide success messages
                if (type === 'success') {
                    setTimeout(() => {
                        alertEl.style.opacity = '0';
                        setTimeout(() => alertEl.remove(), 300);
                    }, 3000);
                }
            }

            function clearAlert() {
                document.getElementById('alert-container').innerHTML = '';
            }

            function setButtonLoading(buttonId, loading) {
                const button = document.getElementById(buttonId);
                if (loading) {
                    button.classList.add('btn-loading');
                    button.disabled = true;
                } else {
                    button.classList.remove('btn-loading');
                    button.disabled = false;
                }
            }

            function getCsrfToken() {
                return document.querySelector('[name=csrfmiddlewaretoken]').value;
            }

            function formatPhoneNumber(input) {
                // Remove all non-digit characters
                let value = input.value.replace(/\D/g, '');

                // Add country code if not present
                if (value.length > 0 && !value.startsWith('1')) {
                    if (value.length === 10) {
                        value = '1' + value;
                    }
                }

                // Format the number
                if (value.length >= 11) {
                    // International format: +1 (XXX) XXX-XXXX
                    value = '+' + value.substring(0, 1) + ' (' + value.substring(1, 4) + ') ' + value.substring(4, 7) + '-' + value.substring(7, 11);
                } else if (value.length >= 7) {
                    // Partial format: +1 (XXX) XXX
                    value = '+' + value.substring(0, 1) + ' (' + value.substring(1, 4) + ') ' + value.substring(4);
                } else if (value.length >= 4) {
                    // Partial format: +1 (XXX
                    value = '+' + value.substring(0, 1) + ' (' + value.substring(1);
                } else if (value.length >= 1) {
                    // Just country code: +1
                    value = '+' + value;
                }

                input.value = value;
            }
        });
    </script>
</body>
</html>
