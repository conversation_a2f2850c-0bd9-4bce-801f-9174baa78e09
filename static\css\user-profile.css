/* User Profile Styles for WallpaperHub */

/* User profile container */
.user-profile {
  position: relative;
  display: flex;
  align-items: center;
  margin-left: 15px;
  cursor: pointer;
}

/* Profile photo styling */
.profile-photo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--primary, #65558F);
  transition: all 0.3s ease;
  background-color: var(--primary, #65558F);
}

.profile-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Hover effects */
.user-profile:hover .profile-photo {
  transform: scale(1.05);
  box-shadow: 0 0 10px rgba(101, 85, 143, 0.5);
}

/* Dropdown menu */
.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 220px;
  background-color: var(--card-bg, #FFFFFF);
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 1000;
  margin-top: 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Show dropdown on hover and when active */
.user-dropdown.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Optional: Also show on hover for desktop users */
@media (min-width: 992px) {
  .user-profile:hover .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

/* Dropdown menu styling */
.user-dropdown {
  padding: 10px 0;
}

.dropdown-header {
  padding: 10px 15px;
  font-weight: 600;
  color: var(--text-color, #333333);
  border-bottom: 1px solid var(--border-color, #e0e0e0);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-divider {
  height: 1px;
  background-color: var(--border-color, #e0e0e0);
  margin: 5px 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  color: var(--text-color, #333333);
  text-decoration: none;
  transition: all 0.2s ease;
}

.dropdown-item i {
  margin-right: 10px;
  color: var(--primary, #65558F);
}

.dropdown-item:hover {
  background-color: rgba(101, 85, 143, 0.1);
}

/* Dark mode adjustments */
[data-theme="dark"] .user-dropdown {
  background-color: var(--card-bg, #161B27);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .dropdown-header {
  color: var(--text-color, #E0E0E0);
  border-bottom: 1px solid var(--border-color, #1E2433);
}

[data-theme="dark"] .dropdown-divider {
  background-color: var(--border-color, #1E2433);
}

[data-theme="dark"] .dropdown-item {
  color: var(--text-color, #E0E0E0);
}

[data-theme="dark"] .dropdown-item:hover {
  background-color: rgba(101, 85, 143, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .user-profile {
    margin-right: 10px;
  }

  .user-profile .user-dropdown {
    display: none;
  }

  .profile-photo {
    width: 32px;
    height: 32px;
  }
}
