# Fixing Google OAuth Sign-In for WallpaperHub

You're encountering a `redirect_uri_mismatch` error when trying to sign in with Google. This error occurs when the redirect URI registered in your Google OAuth credentials doesn't match the actual callback URL that Django is using.

## Step 1: Check Your Current Callback URL

1. Start your Django development server:
   ```
   python manage.py runserver
   ```

2. Visit the OAuth check page:
   ```
   http://127.0.0.1:8000/oauth-check/
   ```

3. Note the callback URL displayed on the page. It should be:
   ```
   http://127.0.0.1:8000/accounts/google/login/callback/
   ```

## Step 2: Update Your Site Domain in Django

1. Run the following management command to update your site domain:
   ```
   python manage.py update_site_domain 127.0.0.1:8000
   ```

## Step 3: Update Google OAuth Credentials

1. Go to the [Google Cloud Console](https://console.cloud.google.com/apis/credentials)
2. Select your project
3. Find and edit your OAuth 2.0 Client ID
4. In the "Authorized redirect URIs" section, add the following URL:
   ```
   http://127.0.0.1:8000/accounts/google/login/callback/
   ```
5. Save your changes

## Step 4: Test the Google Sign-In

1. Go to your login page:
   ```
   http://127.0.0.1:8000/login/
   ```
2. Click the "Sign in with Google" button
3. You should now be able to sign in without the redirect_uri_mismatch error

## Additional Configuration for Production

When you deploy your application to production, you'll need to:

1. Update the site domain to your production domain:
   ```
   python manage.py update_site_domain yourdomain.com
   ```

2. Add your production callback URL to Google OAuth credentials:
   ```
   https://yourdomain.com/accounts/google/login/callback/
   ```

## Troubleshooting

If you're still having issues:

1. Make sure your Google OAuth credentials have the correct JavaScript origins:
   - For development: `http://127.0.0.1:8000`
   - For production: `https://yourdomain.com`

2. Check that your Google OAuth API is enabled in the Google Cloud Console

3. Verify that you're using the correct Client ID and Client Secret in your Django settings

4. Clear your browser cookies and cache before testing again
