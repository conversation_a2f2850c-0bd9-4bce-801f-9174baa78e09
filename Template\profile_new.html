{% load static %}
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ user.username|default:user.email }} - Profile | WallpaperHub</title>

    {% load static %}
    <!-- Favicon -->
    <link rel="icon" href="{% static 'favicon.svg' %}" type="image/svg+xml">
    <link rel="alternate icon" href="{% static 'favicon.ico' %}" type="image/x-icon">
    <link rel="shortcut icon" href="{% static 'favicon.ico' %}" type="image/x-icon">
    <link rel="apple-touch-icon" href="{% static 'apple-touch-icon.png' %}">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">

    <!-- Theme Toggle CSS -->
    <link rel="stylesheet" href="{% static 'css/sun-moon-toggle.css' %}">

    <style>
        /* Dark theme overrides */
        body.dark-theme {
            background-color: #0E121B;
            color: #E0E0E0;
        }

        body.dark-theme header {
            background-color: #161B27;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        body.dark-theme .logo {
            color: #8A7DCE;
        }

        body.dark-theme .nav-link {
            color: #E0E0E0;
        }

        body.dark-theme .nav-link:hover {
            color: #8A7DCE;
        }

        body.dark-theme .profile-header,
        body.dark-theme .upload-form,
        body.dark-theme .empty-state {
            background-color: #161B27;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
        }

        body.dark-theme .profile-info h1 {
            color: #8A7DCE;
        }

        body.dark-theme .profile-info p,
        body.dark-theme .stat-label {
            color: #A0AEC0;
        }

        body.dark-theme .stat-count {
            color: #8A7DCE;
        }

        body.dark-theme .edit-profile-btn {
            background-color: #8A7DCE;
            color: #000000 !important;
        }

        body.dark-theme .edit-profile-btn:hover {
            background-color: #6A5EAE;
            color: #000000 !important;
        }

        body.dark-theme .tab {
            color: #A0AEC0;
        }

        body.dark-theme .tab.active,
        body.dark-theme .tab:hover {
            color: #8A7DCE;
        }

        body.dark-theme .tab.active::after {
            background-color: #8A7DCE;
        }

        body.dark-theme .wallpaper-item {
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
        }

        body.dark-theme .wallpaper-item:hover {
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
        }

        body.dark-theme .loading-placeholder {
            background: linear-gradient(90deg, #161B27 0%, #1E2433 50%, #161B27 100%);
        }

        body.dark-theme .form-control {
            background-color: #1E2433;
            color: #E0E0E0;
            border-color: #2D3748;
        }

        body.dark-theme .form-control:focus {
            border-color: #8A7DCE;
            box-shadow: 0 0 0 2px rgba(138, 125, 206, 0.2);
        }

        body.dark-theme .upload-btn {
            background-color: #8A7DCE;
        }

        body.dark-theme .upload-btn:hover {
            background-color: #6A5EAE;
        }

        body.dark-theme .file-upload-label {
            border-color: #2D3748;
        }

        body.dark-theme .file-upload-label:hover {
            border-color: #8A7DCE;
            background-color: rgba(138, 125, 206, 0.1);
        }

        body.dark-theme .file-upload-label i {
            color: #8A7DCE;
        }

        body.dark-theme .empty-state i {
            color: #2D3748;
        }

        body.dark-theme .empty-state h3 {
            color: #A0AEC0;
        }

        body.dark-theme .empty-state p {
            color: #718096;
        }

        body.dark-theme .empty-state-btn {
            background-color: #8A7DCE;
            color: #000000 !important;
        }

        body.dark-theme .empty-state-btn:hover {
            background-color: #6A5EAE;
            color: #000000 !important;
        }

        body.dark-theme .toast-notification {
            background-color: #161B27;
            color: #E0E0E0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
        }
        :root {
            --primary: #65558F;
            --primary-dark: #534979;
            --secondary: #F8F6FF;
            --dark: #333333;
            --light: #FFFFFF;
            --gray: #F5F5F5;
            --success: #4CAF50;
            --danger: #F44336;
            --warning: #FF9800;
            --info: #2196F3;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--secondary);
            color: var(--dark);
            line-height: 1.6;
        }

        /* Header */
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 30px;
            background-color: var(--light);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            align-items: center;
        }

        .nav-link {
            margin-left: 20px;
            color: var(--dark);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary);
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        /* Profile Header */
        .profile-header {
            background-color: var(--light);
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .profile-photo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            overflow: hidden;
            border: 5px solid var(--primary);
            margin-bottom: 20px;
        }

        .profile-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-info h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--primary);
        }

        .profile-info p {
            color: #777;
            margin-bottom: 15px;
        }

        .profile-stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 20px 0;
        }

        .stat {
            text-align: center;
        }

        .stat-count {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
        }

        .stat-label {
            font-size: 0.9rem;
            color: #777;
        }

        .profile-actions {
            margin-top: 20px;
        }

        .edit-profile-btn {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .edit-profile-btn:hover {
            background-color: var(--primary-dark);
        }

        /* Tabs */
        .profile-tabs {
            display: flex;
            justify-content: center;
            border-bottom: 1px solid #ddd;
            margin-bottom: 30px;
        }

        .tab {
            padding: 15px 30px;
            cursor: pointer;
            font-weight: 500;
            color: #777;
            position: relative;
            transition: color 0.3s ease;
        }

        .tab.active {
            color: var(--primary);
        }

        .tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: var(--primary);
        }

        .tab:hover {
            color: var(--primary);
        }

        /* Tab Content */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Wallpaper Grid */
        .wallpaper-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }

        .wallpaper-item {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .wallpaper-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }

        .wallpaper-link {
            display: block;
            position: relative;
        }

        .img-loading-wrapper {
            position: relative;
            padding-top: 150%; /* 2:3 aspect ratio */
            background-color: #f0f0f0;
        }

        .loading-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, #f0f0f0 0%, #f8f8f8 50%, #f0f0f0 100%);
            background-size: 200% 100%;
            animation: loadingAnimation 1.5s infinite;
        }

        @keyframes loadingAnimation {
            0% { background-position: 0% 0; }
            100% { background-position: -200% 0; }
        }

        .wallpaper-item img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .wallpaper-item:hover img {
            transform: scale(1.05);
        }

        .overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
            padding: 20px;
            color: white;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .wallpaper-item:hover .overlay {
            opacity: 1;
        }

        .overlay h3 {
            font-size: 1rem;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Upload Form */
        .upload-form {
            background-color: var(--light);
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(101, 85, 143, 0.2);
        }

        .upload-btn {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }

        .upload-btn:hover {
            background-color: var(--primary-dark);
        }

        /* File Upload */
        .file-upload {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-upload-label {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 30px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload-label:hover {
            border-color: var(--primary);
            background-color: rgba(101, 85, 143, 0.05);
        }

        .file-upload-label i {
            font-size: 2rem;
            color: var(--primary);
            margin-right: 10px;
        }

        .file-upload input[type="file"] {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .preview-container {
            margin-top: 20px;
            display: none;
        }

        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 5px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 50px 20px;
            background-color: var(--light);
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .empty-state i {
            font-size: 3rem;
            color: #ddd;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #777;
        }

        .empty-state p {
            color: #999;
            margin-bottom: 20px;
        }

        .empty-state-btn {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .empty-state-btn:hover {
            background-color: var(--primary-dark);
        }

        /* Toast Notifications */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }

        .toast-notification {
            display: flex;
            flex-direction: column;
            min-width: 300px;
            max-width: 400px;
            background-color: white;
            color: #333;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
            animation: slideIn 0.5s ease forwards;
            position: relative;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .toast-notification.success {
            border-left: 5px solid var(--success);
        }

        .toast-notification.error {
            border-left: 5px solid var(--danger);
        }

        .toast-content {
            display: flex;
            align-items: center;
            padding: 15px;
        }

        .toast-icon {
            font-size: 24px;
            margin-right: 15px;
        }

        .toast-notification.success .toast-icon {
            color: var(--success);
        }

        .toast-notification.error .toast-icon {
            color: var(--danger);
        }

        .toast-message {
            flex: 1;
        }

        .toast-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #777;
            transition: all 0.3s ease;
            position: absolute;
            top: 10px;
            right: 10px;
        }

        .toast-close:hover {
            color: var(--danger);
            transform: rotate(90deg);
        }

        .toast-progress {
            height: 4px;
            background-color: rgba(0, 0, 0, 0.1);
            width: 100%;
        }

        .toast-progress::before {
            content: '';
            display: block;
            height: 100%;
            background-color: var(--primary);
            animation: progress 5s linear forwards;
        }

        @keyframes progress {
            from { width: 100%; }
            to { width: 0%; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .profile-header {
                padding: 20px;
            }

            .profile-photo {
                width: 100px;
                height: 100px;
            }

            .profile-info h1 {
                font-size: 1.5rem;
            }

            .profile-stats {
                gap: 15px;
            }

            .tab {
                padding: 10px 15px;
                font-size: 0.9rem;
            }

            .wallpaper-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <a href="{% url 'user_home' %}" class="logo">WallpaperHub</a>
        <div class="nav-links">
            <a href="{% url 'user_home' %}" class="nav-link">Home</a>
            <a href="{% url 'logout' %}" class="nav-link">Logout</a>
            <div class="theme-toggle-container">
                <label class="switch">
                    <span class="sun">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <g fill="#ffd43b">
                                <circle r="5" cy="12" cx="12"></circle>
                                <path d="m21 13h-1a1 1 0 0 1 0-2h1a1 1 0 0 1 0 2zm-17 0h-1a1 1 0 0 1 0-2h1a1 1 0 0 1 0 2zm13.66-5.66a1 1 0 0 1 -.66-.29 1 1 0 0 1 0-1.41l.71-.71a1 1 0 1 1 1.41 1.41l-.71.71a1 1 0 0 1 -.75.29zm-12.02 12.02a1 1 0 0 1 -.71-.29 1 1 0 0 1 0-1.41l.71-.66a1 1 0 0 1 1.41 1.41l-.71.71a1 1 0 0 1 -.7.24zm6.36-14.36a1 1 0 0 1 -1-1v-1a1 1 0 0 1 2 0v1a1 1 0 0 1 -1 1zm0 17a1 1 0 0 1 -1-1v-1a1 1 0 0 1 2 0v1a1 1 0 0 1 -1 1zm-5.66-14.66a1 1 0 0 1 -.7-.29l-.71-.71a1 1 0 0 1 1.41-1.41l.71.71a1 1 0 0 1 0 1.41 1 1 0 0 1 -.71.29zm12.02 12.02a1 1 0 0 1 -.7-.29l-.66-.71a1 1 0 0 1 1.36-1.36l.71.71a1 1 0 0 1 0 1.41 1 1 0 0 1 -.71.24z"></path>
                            </g>
                        </svg>
                    </span>
                    <span class="moon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
                            <path d="m223.5 32c-123.5 0-223.5 100.3-223.5 224s100 224 223.5 224c60.6 0 115.5-24.2 155.8-63.4 5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6-96.9 0-175.5-78.8-175.5-176 0-65.8 36-123.1 89.3-153.3 6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z"></path>
                        </svg>
                    </span>
                    <input type="checkbox" class="input" id="profile-theme-toggle">
                    <span class="slider"></span>
                </label>
            </div>
        </div>
    </header>

    <div class="container">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="profile-photo">
                {% if profile_image %}
                    <img src="{{ profile_image }}?v={{ timestamp }}" alt="{{ username|default:user.email }}">
                {% else %}
                    <img src="https://ui-avatars.com/api/?name={{ user.email|urlencode }}&background=65558f&color=fff&size=128" alt="{{ username|default:user.email }}">
                {% endif %}
            </div>
            <div class="profile-info">
                <h1>{{ username|default:user.email }}</h1>
                <p>{{ bio|default:"No bio yet" }}</p>
            </div>
            <div class="profile-stats">
                <div class="stat">
                    <div class="stat-count">{{ saved_wallpapers|length }}</div>
                    <div class="stat-label">Saved</div>
                </div>
                <div class="stat">
                    <div class="stat-count">{{ uploaded_wallpapers|length }}</div>
                    <div class="stat-label">Uploads</div>
                </div>
                <div class="stat">
                    <div class="stat-count">{{ liked_wallpapers|length }}</div>
                    <div class="stat-label">Liked</div>
                </div>
            </div>
            <div class="profile-actions">
                <a href="{% url 'edit_profile' %}" class="edit-profile-btn">Edit Profile</a>
            </div>
        </div>

        <!-- Profile Tabs -->
        <div class="profile-tabs">
            <div class="tab active" data-tab="saved">Saved</div>
            <div class="tab" data-tab="uploads">Uploads</div>
            <div class="tab" data-tab="liked">Liked</div>
            <div class="tab" data-tab="upload">Upload New</div>
        </div>

        <!-- Saved Wallpapers Tab -->
        <div class="tab-content active" id="saved-tab">
            {% if saved_wallpapers %}
            <div class="wallpaper-grid">
                {% for wallpaper in saved_wallpapers %}
                <div class="wallpaper-item">
                    <a href="{% url 'wallpaper_detail' id=wallpaper.id %}" class="wallpaper-link">
                        <div class="img-loading-wrapper">
                            <div class="loading-placeholder"></div>
                            <img src="{{ wallpaper.urls.small }}" alt="{{ wallpaper.alt_description|default:'Beautiful Wallpaper' }}" loading="lazy">
                        </div>
                        <div class="overlay">
                            <h3>{{ wallpaper.alt_description|default:'Beautiful Wallpaper' }}</h3>
                        </div>
                    </a>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <i class="bi bi-bookmark"></i>
                <h3>No saved wallpapers yet</h3>
                <p>Save wallpapers you like to view them later</p>
                <a href="{% url 'user_home' %}" class="empty-state-btn">Explore Wallpapers</a>
            </div>
            {% endif %}
        </div>

        <!-- Uploaded Wallpapers Tab -->
        <div class="tab-content" id="uploads-tab">
            {% if uploaded_wallpapers %}
            <div class="wallpaper-grid">
                {% for wallpaper in uploaded_wallpapers %}
                <div class="wallpaper-item">
                    <a href="{% url 'wallpaper_detail' id=wallpaper.id %}" class="wallpaper-link">
                        <div class="img-loading-wrapper">
                            <div class="loading-placeholder"></div>
                            {% if wallpaper.custom_upload %}
                                <img src="{{ wallpaper.image_path }}" alt="{{ wallpaper.title }}" loading="lazy">
                            {% else %}
                                <img src="{{ wallpaper.urls.small }}" alt="{{ wallpaper.alt_description|default:'Beautiful Wallpaper' }}" loading="lazy">
                            {% endif %}
                        </div>
                        <div class="overlay">
                            {% if wallpaper.custom_upload %}
                                <h3>{{ wallpaper.title }}</h3>
                            {% else %}
                                <h3>{{ wallpaper.alt_description|default:'Beautiful Wallpaper' }}</h3>
                            {% endif %}
                        </div>
                    </a>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <i class="bi bi-cloud-upload"></i>
                <h3>No uploads yet</h3>
                <p>Share your beautiful wallpapers with the community</p>
                <button class="empty-state-btn" onclick="switchTab('upload')">Upload Now</button>
            </div>
            {% endif %}
        </div>

        <!-- Liked Wallpapers Tab -->
        <div class="tab-content" id="liked-tab">
            {% if liked_wallpapers %}
            <div class="wallpaper-grid">
                {% for wallpaper in liked_wallpapers %}
                <div class="wallpaper-item">
                    <a href="{% url 'wallpaper_detail' id=wallpaper.id %}" class="wallpaper-link">
                        <div class="img-loading-wrapper">
                            <div class="loading-placeholder"></div>
                            <img src="{{ wallpaper.urls.small }}" alt="{{ wallpaper.alt_description|default:'Beautiful Wallpaper' }}" loading="lazy">
                        </div>
                        <div class="overlay">
                            <h3>{{ wallpaper.alt_description|default:'Beautiful Wallpaper' }}</h3>
                        </div>
                    </a>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <i class="bi bi-heart"></i>
                <h3>No liked wallpapers yet</h3>
                <p>Like wallpapers to save them to this collection</p>
                <a href="{% url 'user_home' %}" class="empty-state-btn">Explore Wallpapers</a>
            </div>
            {% endif %}
        </div>

        <!-- Upload New Tab -->
        <div class="tab-content" id="upload-tab">
            <div class="upload-form">
                <h2>Upload New Wallpaper</h2>
                <p>Share your beautiful wallpapers with the community</p>

                <form action="{% url 'upload_wallpaper' %}" method="POST" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="form-group">
                        <label class="form-label">Wallpaper Image</label>
                        <div class="file-upload">
                            <label class="file-upload-label">
                                <i class="bi bi-cloud-upload"></i>
                                <span id="file-name">Choose a file or drag it here</span>
                            </label>
                            <input type="file" name="wallpaper_image" id="image-upload" accept="image/*" required>
                        </div>
                        <div class="preview-container" id="preview-container">
                            <img src="" alt="Preview" class="preview-image" id="preview-image">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="title" class="form-label">Title</label>
                        <input type="text" id="title" name="title" class="form-control" placeholder="Give your wallpaper a title" required>
                    </div>

                    <div class="form-group">
                        <label for="description" class="form-label">Description</label>
                        <textarea id="description" name="description" class="form-control" rows="4" placeholder="Describe your wallpaper"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="tags" class="form-label">Tags</label>
                        <input type="text" id="tags" name="tags" class="form-control" placeholder="Add tags separated by commas (e.g. nature, mountains, sunset)">
                    </div>

                    <button type="submit" class="upload-btn">Upload Wallpaper</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container" id="toast-container">
        {% if messages %}
        {% for message in messages %}
        <div class="toast-notification {{ message.tags }}">
            <div class="toast-content">
                <i class="toast-icon bi {% if message.tags == 'success' %}bi-check-circle-fill{% else %}bi-exclamation-circle-fill{% endif %}"></i>
                <div class="toast-message">{{ message }}</div>
            </div>
            <button class="toast-close">&times;</button>
            <div class="toast-progress"></div>
        </div>
        {% endfor %}
        {% endif %}
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab switching
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');

                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));

                    // Add active class to current tab and content
                    this.classList.add('active');
                    document.getElementById(`${tabId}-tab`).classList.add('active');
                });
            });

            // Image upload preview
            const imageUpload = document.getElementById('image-upload');
            const previewContainer = document.getElementById('preview-container');
            const previewImage = document.getElementById('preview-image');
            const fileName = document.getElementById('file-name');

            imageUpload.addEventListener('change', function() {
                const file = this.files[0];

                if (file) {
                    const reader = new FileReader();

                    reader.addEventListener('load', function() {
                        previewImage.setAttribute('src', this.result);
                        previewContainer.style.display = 'block';
                    });

                    fileName.textContent = file.name;
                    reader.readAsDataURL(file);
                } else {
                    previewContainer.style.display = 'none';
                    fileName.textContent = 'Choose a file or drag it here';
                }
            });

            // Toast notifications
            const toastCloseButtons = document.querySelectorAll('.toast-close');

            toastCloseButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const toast = this.parentElement;
                    toast.style.animation = 'slideOut 0.5s ease forwards';

                    setTimeout(() => {
                        toast.remove();
                    }, 500);
                });
            });

            // Auto-remove toasts after 5 seconds
            const toasts = document.querySelectorAll('.toast-notification');

            toasts.forEach(toast => {
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.style.animation = 'slideOut 0.5s ease forwards';

                        setTimeout(() => {
                            if (toast.parentElement) {
                                toast.remove();
                            }
                        }, 500);
                    }
                }, 5000);
            });
        });

        // Function to switch tabs programmatically
        function switchTab(tabId) {
            const tab = document.querySelector(`.tab[data-tab="${tabId}"]`);
            if (tab) {
                tab.click();
            }
        }
    </script>

    <!-- Theme Toggle Script -->
    <script src="{% static 'js/sun-moon-toggle.js' %}"></script>
</body>
</html>
